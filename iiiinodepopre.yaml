apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: karpenter-test2
spec:
  weight: 80
  disruption:
    budgets:
      - nodes: 10%
    consolidateAfter: 30m0s
    consolidationPolicy: WhenEmpty
  limits:
    cpu: 64
    memory: 30Gi
  template:
    metadata:
      labels:
        servicegroup: karpenter-test2
    spec:
      expireAfter: Never
      nodeClassRef:
        group: karpenter.k8s.oracle
        kind: OciNodeClass
        name: karpenter-test2
      requirements:
        - key: karpenter.sh/capacity-type
          operator: In
          values:
            - preemptible
        - key: karpenter.k8s.oracle/instance-shape-name
          operator: In
          values:
            - VM.Standard.E4.Flex
        - key: karpenter.k8s.oracle/instance-cpu
          operator: In
          values:
            - '4'
            - '8'
            - '16'
        - key: kubernetes.io/os
          operator: In
          values:
            - linux
      terminationGracePeriod: 30m
---
apiVersion: karpenter.k8s.oracle/v1alpha1
kind: OciNodeClass
metadata:
  name: karpenter-test2
spec:
  bootConfig:
    bootVolumeSizeInGBs: 100
    bootVolumeVpusPerGB: 10
  agentList:
    - Bastion
  imageSelector:
    - name: okeworker
      compartmentId: ocid1.compartment.oc1..aaaaaaaau5q457a7teqkjce4oenoiz6bmc4g3s74a5543iqbm7xwplho44fq
  imageFamily: Custom
  kubelet:
    evictionHard:
      imagefs.available: 15%
      imagefs.inodesFree: 10%
      memory.available: 750Mi
      nodefs.available: 10%
      nodefs.inodesFree: 5%
    systemReserved:
      memory: 100Mi
  subnetSelector:
    - name:  oke-nodesubnet-quick-manage-7d568cca1-regional
  vcnId:  ocid1.vcn.oc1.ap-melbourne-1.amaaaaaaak7gbriaii4v47wvdr2uvv4jehg46hoxz6z3v4ovm326ercnweha
  userData: |
    #!/bin/bash -xe
    bash /etc/oke/oke-install.sh --apiserver-endpoint '10.0.0.9' --kubelet-ca-cert 'LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURpVENDQW5HZ0F3SUJBZ0lSQUl3N1hQa0dhOVMyZm1UZjRxS2p6ZXN3RFFZSktvWklodmNOQVFFTEJRQXcKWGpFUE1BMEdBMVVFQXd3R1N6aHpJRU5CTVFzd0NRWURWUVFHRXdKVlV6RVBNQTBHQTFVRUJ3d0dRWFZ6ZEdsdQpNUTh3RFFZRFZRUUtEQVpQY21GamJHVXhEREFLQmdOVkJBc01BMDlqYVRFT01Bd0dBMVVFQ0F3RlZHVjRZWE13CkhoY05NalV3TlRFMU1EVXpNakl5V2hjTk16QXdOVEUxTURVek1qSXlXakJlTVE4d0RRWURWUVFEREFaTE9ITWcKUTBFeEN6QUpCZ05WQkFZVEFsVlRNUTh3RFFZRFZRUUhEQVpCZFhOMGFXNHhEekFOQmdOVkJBb01Cazl5WVdOcwpaVEVNTUFvR0ExVUVDd3dEVDJOcE1RNHdEQVlEVlFRSURBVlVaWGhoY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCCkJRQURnZ0VQQURDQ0FRb0NnZ0VCQU0xSzg2NExwWndIWXRGdTl3a1MrdTM3WE1JSkNVQ0JiU3VVek5BMURETzgKemNxOXJWek0yRVRyUjJCVXJZK0laRUNOOXdhV2hncHY3OHBqMEtaeUlKakh5UVBlWVhjN2RpMUpuVWVtYjY5cwpuTXpmTG5OSkRWYURDc3JQVGhYQWNTZXBUMEVqTlJjRStCR3ptb1dzYlAzcGRBL1NwcGFJbE5qeFczQ2FldFZGCnFCa0JjZ1M0c3VOTGNZSzNhT3FKRkw2SGU2Mkc0ZDRaTlhBanpLa3p0SUFSaEM2WXlWUHV5dEVLb0xVcUtuVjIKSllkR3d5RUIrQkZMYlo4QkVLK0JWMmNGUENOc05zek1nZXlSeDNYeEdOaEx5UiszR0UzaGRwVTMvZktwUlhRegpBMzhkZXpzUk5KUTZXYmZReFhDTUUrMmV6MTBpYlUrc3Z5VytWb1BCS2swQ0F3RUFBYU5DTUVBd0R3WURWUjBUCkFRSC9CQVV3QXdFQi96QU9CZ05WSFE4QkFmOEVCQU1DQVFZd0hRWURWUjBPQkJZRUZFKy9lUFJlUmUyVkdlNHYKOTRQa0RIY0JkSUpNTUEwR0NTcUdTSWIzRFFFQkN3VUFBNElCQVFDRjUrOEJrM2hLcktYMEJLZzd3M1VLUTM2WApUT3hXWnA3Q1pNRVNvcWtjb2k2bWZYRU56a1VQM3Z5QTNSQzNjM3psaytSY3NrR2lYSHN5M2k5L3BpY2xFMG5JClo2VDRZUktpWit4QmhUc0pxaGs1Nk94aktBTVhNMW1UaVJnM3RlV1BYZEEyTHhlRDRucldLNWoxbXlEZUNaeGoKZGoyVEJLZUlPRW5hdzVjenJDUnhOWGZhamFnWER6dk5IUmlabkRZNDZoamZkSS9tT3hJVVo4bEppeUo3VTdDcAppSUkwVjFCQzdmU2dBWmtocFZCQzA5ZU02R0RuZERVMFRtTTJ1OEJjbDg5dkFXdUJMRS9uVVJVTEdINFYrRmlrCkZ0TDhIdFducmx1QlZLRmc2Nzl1MDhpV3Rya2VKUUhoQnhWUGgxRktKZnFvdG1GVG9TeTVQNzVZSWk2aQotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==' \
    –kubelet-extra-args '–node-labels="karpenter.sh/nodepool=karpenter-test,servicegroup=karpenter-test" --max-pods=110 --system-reserved="memory=100Mi" --eviction-hard="imagefs.inodesFree<10%,memory.available<750Mi,nodefs.available<10%,nodefs.inodesFree<5%,imagefs.available<15%"' \
    –cluster-dns '10.96.5.5'
