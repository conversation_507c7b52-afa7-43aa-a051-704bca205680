apiVersion: apps/v1
kind: DaemonSet
metadata:
  annotations:
    deprecated.daemonset.template.generation: "5"
  creationTimestamp: "2025-06-17T02:21:20Z"
  generation: 5
  labels:
    app: flannel
    tier: node
  name: kube-flannel-ds
  namespace: kube-system
  resourceVersion: "540581"
  uid: d35c28fd-65fc-42a8-bb54-1dc58f58da2c
spec:
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: flannel
      tier: node
  template:
    metadata:
      annotations:
        checksum/config: b1655542c5171d68
        version_hash: "-596598035"
      creationTimestamp: null
      labels:
        app: flannel
        tier: node
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: oci.oraclecloud.com/vcn-native-ip-cni
                operator: NotIn
                values:
                - "true"
              - key: oci.oraclecloud.com/custom-k8s-networking
                operator: NotIn
                values:
                - "true"
              - key: node-role.kubernetes.io/virtual-node
                operator: DoesNotExist
      containers:
      - args:
        - --ip-masq
        - --kube-subnet-mgr
        command:
        - /opt/bin/flanneld
        env:
        - name: KUBERNETES_SERVICE_HOST
          value: *********
        - name: KUBERNETES_SERVICE_PORT
          value: "6443"
        - name: POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
        - name: PATH
          value: /hostIptables:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/host/usr/bin:/host/sbin
        image: ap-melbourne-1.ocir.io/axoxdievda5j/oke-public-flannel@sha256:bd2a3a7eadcde53b82f2dbaedf19d81870d30208b9cb94e15e5c11231e3b1d15
        imagePullPolicy: IfNotPresent
        name: kube-flannel
        resources:
          limits:
            cpu: "1"
            memory: 500Mi
          requests:
            cpu: 100m
            memory: 50Mi
        securityContext:
          allowPrivilegeEscalation: true
          privileged: true
          readOnlyRootFilesystem: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /run
          name: run
        - mountPath: /etc/kube-flannel/
          name: flannel-cfg
        - mountPath: /hostIptables/
          name: chroot-iptables
        - mountPath: /host
          name: host-root
      dnsPolicy: ClusterFirst
      hostNetwork: true
      initContainers:
      - command:
        - /bin/bash
        - -ce
        - echo Validating if flannel plugin needs to be copied to host; if [ -f /tmp/flannel
          ]; then echo Copying flannel plugin && cp -f /tmp/flannel /opt/cni/bin/;
          fi
        image: ap-melbourne-1.ocir.io/axoxdievda5j/oke-public-flannel@sha256:bd2a3a7eadcde53b82f2dbaedf19d81870d30208b9cb94e15e5c11231e3b1d15
        imagePullPolicy: IfNotPresent
        name: install-bin
        resources: {}
        securityContext:
          allowPrivilegeEscalation: true
          privileged: true
          readOnlyRootFilesystem: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /opt/cni/bin
          name: host-cni-bin
      - args:
        - -f
        - /etc/kube-flannel/cni-conf.json
        - /etc/cni/net.d/10-flannel.conflist
        command:
        - cp
        env:
        - name: PATH
          value: /hostIptables:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/host/usr/bin:/host/sbin
        image: ap-melbourne-1.ocir.io/axoxdievda5j/oke-public-flannel@sha256:bd2a3a7eadcde53b82f2dbaedf19d81870d30208b9cb94e15e5c11231e3b1d15
        imagePullPolicy: IfNotPresent
        name: install-cni
        resources: {}
        securityContext:
          allowPrivilegeEscalation: true
          privileged: true
          readOnlyRootFilesystem: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /etc/cni/net.d
          name: cni
        - mountPath: /etc/kube-flannel/
          name: flannel-cfg
        - mountPath: /hostIptables/
          name: chroot-iptables
        - mountPath: /host
          name: host-root
      priorityClassName: system-node-critical
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext:
        runAsNonRoot: false
        runAsUser: 0
      serviceAccount: flannel
      serviceAccountName: flannel
      terminationGracePeriodSeconds: 30
      tolerations:
      - key: CriticalAddonsOnly
        operator: Exists
      - operator: Exists
      volumes:
      - hostPath:
          path: /run
          type: ""
        name: run
      - hostPath:
          path: /etc/cni/net.d
          type: ""
        name: cni
      - configMap:
          defaultMode: 420
          name: kube-flannel-cfg
        name: flannel-cfg
      - hostPath:
          path: /opt/cni/bin
          type: Directory
        name: host-cni-bin
      - hostPath:
          path: /
          type: Directory
        name: host-root
      - configMap:
          defaultMode: 493
          name: oci-iptables-flannel
        name: chroot-iptables
  updateStrategy:
    rollingUpdate:
      maxSurge: 0
      maxUnavailable: 1
    type: RollingUpdate
status:
  currentNumberScheduled: 1
  desiredNumberScheduled: 1
  numberAvailable: 1
  numberMisscheduled: 0
  numberReady: 1
  observedGeneration: 5
  updatedNumberScheduled: 1
