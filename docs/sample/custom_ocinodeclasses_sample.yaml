apiVersion: karpenter.k8s.oracle/v1alpha1
kind: OciNodeClass
metadata:
  name: karpenter-test-custom
spec:
  bootConfig:
    bootVolumeSizeInGBs: 100
    bootVolumeVpusPerGB: 10
  agentList:
    - Bastion
  imageSelector:
    - name: Oracle-Linux-8.10-2025.02.28-0-OKE-1.30.1-760
      compartmentId: ocid1.compartment.oc1..aaaaaaaab4u67dhgtj5gpdpp3z42xqqsdnufxkatoild46u3hb67vzojfmzq
  imageFamily: Custom
  kubelet:
    evictionHard:
      imagefs.available: 15%
      imagefs.inodesFree: 10%
      memory.available: 750Mi
      nodefs.available: 10%
      nodefs.inodesFree: 5%
    systemReserved:
      memory: 100Mi
  subnetSelector:
    - name: {{ .subnetName }}
  vcnId: {{ .vcnId }}
  userData: |
    #!/bin/bash -xe
    bash /etc/oke/oke-install.sh --apiserver-endpoint '10.0.0.10' --kubelet-ca-cert '==' \
    --kubelet-extra-args '--node-labels="karpenter.sh/nodepool=karpenter-test,servicegroup=karpenter-test" --max-pods=110 --system-reserved="memory=100Mi" --eviction-hard="imagefs.inodesFree<10%,memory.available<750Mi,nodefs.available<10%,nodefs.inodesFree<5%,imagefs.available<15%"' \
    --cluster-dns '10.96.5.5'