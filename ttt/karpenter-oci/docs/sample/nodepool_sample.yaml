apiVersion: karpenter.sh/v1
kind: NodePool
metadata:
  name: karpenter-test
spec:
  disruption:
    budgets:
      - nodes: 10%
    consolidateAfter: 30m0s
    consolidationPolicy: WhenEmpty
  limits:
    cpu: 64
    memory: 300Gi
  template:
    metadata:
      labels:
        servicegroup: karpenter-test
    spec:
      expireAfter: Never
      nodeClassRef:
        group: karpenter.k8s.oracle
        kind: OciNodeClass
        name: karpenter-test
      requirements:
        - key: karpenter.sh/capacity-type
          operator: In
          values:
            - on-demand
        - key: karpenter.k8s.oracle/instance-shape-name
          operator: In
          values:
            - VM.Standard.E4.Flex
        - key: karpenter.k8s.oracle/instance-cpu
          operator: In
          values:
            - '4'
            - '8'
            - '16'
        - key: kubernetes.io/os
          operator: In
          values:
            - linux
      terminationGracePeriod: 30m
