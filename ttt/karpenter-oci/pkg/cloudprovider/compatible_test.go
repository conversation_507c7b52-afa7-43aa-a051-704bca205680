/*
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package cloudprovider

import (
	"github.com/onsi/ginkgo/v2"
	"github.com/onsi/gomega"
	corev1 "k8s.io/api/core/v1"
	"sigs.k8s.io/karpenter/pkg/scheduling"
	"testing"
)

func TestCompatible(t *testing.T) {

	exists := scheduling.NewRequirements(scheduling.NewRequirement(corev1.LabelTopologyZone, corev1.NodeSelectorOpExists))
	doesNotExist := scheduling.NewRequirements(scheduling.NewRequirement(corev1.LabelTopologyZone, corev1.NodeSelectorOpDoesNotExist))

	aExist := scheduling.NewRequirements(scheduling.NewRequirement("a", corev1.NodeSelectorOpExists))
	gomega.RegisterFailHandler(ginkgo.Fail)

	gomega.Expect(exists.Compatible(doesNotExist)).NotTo(gomega.BeNil())
	gomega.Expect(aExist.Compatible(doesNotExist)).To(gomega.BeNil())

}
