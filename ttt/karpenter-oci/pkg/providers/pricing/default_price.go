/*
Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package pricing

var defaultPrice = `
{
  "items": [
    {
      "partNumber": "B107951",
      "displayName": "Oracle Exadata Exascale VM Filesystem Storage",
      "metricName": "Gigabyte (GB) Storage Capacity Per Month",
      "serviceCategory": "Exadata Exascale Infrastructure",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0425
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B107952",
      "displayName": "Oracle Exadata Exascale Smart Database Storage",
      "metricName": "Gigabyte (GB) Storage Capacity Per Month",
      "serviceCategory": "Exadata Exascale Infrastructure",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1156
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B107975",
      "displayName": "OCI - FastConnect 400 Gbps",
      "metricName": "Port Hour",
      "serviceCategory": "Networking - FastConnect",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 20
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108004",
      "displayName": "OCI Managed Service for Mac - M2 Pro",
      "metricName": "Mac Server Per Hour",
      "serviceCategory": "Managed Services for Mac",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.74
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108030",
      "displayName": "MySQL Database - ECPU",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "MySQL",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0366
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108077",
      "displayName": "OCI Generative AI - Large Cohere",
      "metricName": "10,000 Transactions",
      "serviceCategory": "OCI Generative AI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0156
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108078",
      "displayName": "OCI Generative AI - Small Cohere",
      "metricName": "10,000 Transactions",
      "serviceCategory": "OCI Generative AI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0009
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108079",
      "displayName": "OCI Generative AI - Embed Cohere",
      "metricName": "10,000 Transactions",
      "serviceCategory": "OCI Generative AI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.001
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108080",
      "displayName": "OCI Generative AI - Large Meta",
      "metricName": "10,000 Transactions",
      "serviceCategory": "OCI Generative AI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0018
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108082",
      "displayName": "OCI Generative AI - Large Cohere - Dedicated",
      "metricName": "AI Unit Per Hour",
      "serviceCategory": "OCI Generative AI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 24
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108083",
      "displayName": "OCI Generative AI - Small Cohere - Dedicated",
      "metricName": "AI Unit Per Hour",
      "serviceCategory": "OCI Generative AI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 6.5
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108084",
      "displayName": "OCI Generative AI - Embed Cohere - Dedicated",
      "metricName": "AI Unit Per Hour",
      "serviceCategory": "OCI Generative AI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 10.9
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108085",
      "displayName": "OCI Generative AI - Large Meta - Dedicated",
      "metricName": "AI Unit Per Hour",
      "serviceCategory": "OCI Generative AI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 12
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108130",
      "displayName": "Oracle Backend for Spring Boot and Microservices - Standard Edition - Marketplace",
      "metricName": "Each",
      "serviceCategory": "Oracle Backend for Microservices and AI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108188",
      "displayName": "Oracle Cloud Guard Instance Security Enterprise",
      "metricName": "Node Per Hour",
      "serviceCategory": "Cloud Guard - Security",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0069
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108189",
      "displayName": "Oracle Cloud Guard Instance Security Standard",
      "description": "2 free detections per node hour",
      "metricName": "Node Per Hour",
      "serviceCategory": "Cloud Guard - Security",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108190",
      "displayName": "Oracle Cloud Guard Instance Security Adhoc Queries Enterprise",
      "metricName": "Request",
      "serviceCategory": "Cloud Guard - Security",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 950000
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.001,
              "rangeMin": 950000,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108711",
      "displayName": "OCI - Language - Dedicated Inferencing - Healthcare",
      "metricName": "Inferencing Unit Hour",
      "serviceCategory": "OCI Language",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 20
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108764",
      "displayName": "OCI - Vulnerability Detection and Patching - External Databases",
      "metricName": "Host CPU Core Per Hour",
      "serviceCategory": "Vulnerability Detection and Patching",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.05
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108765",
      "displayName": "OCI - Vulnerability Detection and Patching - External Databases BYOL",
      "metricName": "Host CPU Core Per Hour",
      "serviceCategory": "Vulnerability Detection and Patching",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.025
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108773",
      "displayName": "OCI - SQL Performance Watch External DB",
      "metricName": "Host CPU Core Per Month",
      "serviceCategory": "Oracle Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 40
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108776",
      "displayName": "WebCenter Imaging For OCI",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "WebCenter For OCI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.6394
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108777",
      "displayName": "WebCenter Enterprise Capture For OCI",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "WebCenter For OCI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.417
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108778",
      "displayName": "WebCenter Enterprise Capture Standard Edition For OCI",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "WebCenter For OCI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.2085
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108779",
      "displayName": "WebCenter Sites For OCI",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "WebCenter For OCI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.695
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108780",
      "displayName": "WebCenter Sites Satellite Server For OCI",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "WebCenter For OCI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1738
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108781",
      "displayName": "WebCenter Portal For OCI",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "WebCenter For OCI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.8688
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108782",
      "displayName": "WebCenter Forms Recognition For OCI",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "WebCenter For OCI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.695
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108783",
      "displayName": "WebCenter Content For OCI",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "WebCenter For OCI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.1989
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108784",
      "displayName": "WebCenter Universal Content Management For OCI",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "WebCenter For OCI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.7993
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108806",
      "displayName": "Oracle Cloud VMware Solution - BM.GPU.A10.64 - Monthly Commit",
      "metricName": "Node Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 16
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108807",
      "displayName": "Oracle Cloud VMware Solution - BM.GPU.A10.64 - 1 Year Commit",
      "metricName": "Node Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 13
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108808",
      "displayName": "Oracle Cloud VMware Solution - BM.GPU.A10.64 - 3 Year Commit",
      "metricName": "Node Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 11
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108809",
      "displayName": "Oracle Cloud VMware Solution - BM.Standard.E5.48 - Hourly Commit",
      "metricName": "Node Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 16.6617
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108810",
      "displayName": "Oracle Cloud VMware Solution - BM.Standard.E5.48 - 1 Year Commit",
      "metricName": "Node Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 13.1604
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B108811",
      "displayName": "Oracle Cloud VMware Solution - BM.Standard.E5.48 - 3 Year Commit",
      "metricName": "Node Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 10.6119
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109166",
      "displayName": "MySQL HeatWave - AWS - Storage",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "HeatWave Cluster",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.03
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109169",
      "displayName": "MySQL Database - Outbound Data Transfer - Inter OCI Region",
      "metricName": "Gigabyte Outbound Data Transfer Per Month",
      "serviceCategory": "MySQL",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.04
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109187",
      "displayName": "MySQL Database - AWS - Ingress private endpoint",
      "metricName": "Endpoint Per Hour",
      "serviceCategory": "Database - MySQL HeatWave on AWS",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0214
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109188",
      "displayName": "MySQL Database - AWS - Egress private endpoint",
      "metricName": "Endpoint Per Hour",
      "serviceCategory": "Database - MySQL HeatWave on AWS",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0106
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109355",
      "displayName": "Oracle Exadata Exascale RDMA Compute Infrastructure",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Exadata Exascale Infrastructure",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.025
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109356",
      "displayName": "Oracle Exadata Exascale Database ECPU",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Exadata Database Service ECPUs",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.336
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109357",
      "displayName": "Oracle Exadata Exascale Database ECPU - BYOL",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Exadata Database Service ECPUs",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0807
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109358",
      "displayName": "OCI - MySQL Database - Oracle Ops Insights for MySQL HeatWave",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Observability - Ops Insights",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.015
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109359",
      "displayName": "OCI - MySQL Database - Oracle Ops Insights for MySQL HeatWave",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Observability - Ops Insights",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0075
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109375",
      "displayName": "Oracle Exadata Exascale Additional Flash Cache",
      "metricName": "Gigabyte Per Hour",
      "serviceCategory": "Exadata Exascale Infrastructure",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0005
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109380",
      "displayName": "MySQL Database - ECPU - Free",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "HeatWave Free Tier",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109381",
      "displayName": "OCI HeatWave - Free",
      "metricName": "HeatWave Capacity Per Hour",
      "serviceCategory": "HeatWave Free Tier",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109382",
      "displayName": "OCI - HeatWave - Storage - Free",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "HeatWave Free Tier",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109383",
      "displayName": "MySQL Database - Storage - Free",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "HeatWave Free Tier",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109384",
      "displayName": "MySQL Database - Backup Storage - Free",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "HeatWave Free Tier",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109458",
      "displayName": "MySQL Database - AWS - Private inbound and outbound network traffic",
      "metricName": "Gigabyte (GB) of Data Transferred",
      "serviceCategory": "Database - MySQL HeatWave on AWS",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0086
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109479",
      "displayName": "Compute - GPU - L40S",
      "metricName": "GPU Per Hour",
      "serviceCategory": "Compute - GPU",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 3.5
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109485",
      "displayName": "OCI - Compute - GPU - MI300X",
      "metricName": "GPU Per Hour",
      "serviceCategory": "Compute - GPU",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 6
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109492",
      "displayName": "Roving Edge Device - RED.2 Compute",
      "metricName": "Resource Possession Per Day",
      "serviceCategory": "Roving Edge Infrastructure",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 80
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109493",
      "displayName": "Roving Edge Device - RED.2 GPU",
      "metricName": "Resource Possession Per Day",
      "serviceCategory": "Roving Edge Infrastructure",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 100
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109494",
      "displayName": "Roving Edge Device - RED.2.STG Storage",
      "metricName": "Resource Possession Per Day",
      "serviceCategory": "Roving Edge Infrastructure",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 95
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109495",
      "displayName": "Roving Edge Device - RED.2 Non-Return Fee",
      "metricName": "Each",
      "serviceCategory": "Roving Edge Infrastructure",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 25000
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109496",
      "displayName": "Roving Edge Device - RED.2 Ruggedized Case",
      "metricName": "Resource Possession Per Day",
      "serviceCategory": "Roving Edge Infrastructure",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 10
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109529",
      "displayName": "Compute - Standard - A2 OCPU",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.014
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109530",
      "displayName": "Compute - Standard - A2 Memory",
      "metricName": "Gigabyte Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.002
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109545",
      "displayName": "Oracle Blockchain Platform Enterprise Edition for  OCI",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Application Development - Blockchain",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.4301
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109546",
      "displayName": "File Storage Service - High Performance Mount Target",
      "metricName": "Performance Units Per Gigabyte Per Month",
      "serviceCategory": "Storage - File Storage",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.3
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109559",
      "displayName": "Oracle Integration Cloud Service - Healthcare",
      "metricName": "5K Messages Per Hour",
      "serviceCategory": "Application Integration - OIC",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.9355
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109565",
      "displayName": "OCI - Blockchain Platform Cloud Service - Digital Assets",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Application Development - Blockchain",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.0161
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B109635",
      "displayName": "Oracle Base Database Service on Ampere A1 - Developer",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Database - Base Database Service - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.022
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110274",
      "displayName": "OCI Full Stack Disaster Recovery Service",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Full Stack Disaster Recovery",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0032
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110316",
      "displayName": "Oracle Autonomous Database - Developer",
      "metricName": "Instance Per Hour",
      "serviceCategory": "Database - Autonomous Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0391
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110461",
      "displayName": "OCI Generative AI Agents",
      "metricName": "10,000 Transactions",
      "serviceCategory": "OCI Generative AI Agents",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.003
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110462",
      "displayName": "OCI Generative AI Agents - Knowledge Base Storage",
      "metricName": "Gigabyte Storage Per Hour",
      "serviceCategory": "OCI Generative AI Agents",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0084
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110463",
      "displayName": "OCI Generative AI Agents - Data Ingestion",
      "metricName": "10,000 Transactions",
      "serviceCategory": "OCI Generative AI Agents",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0003
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110464",
      "displayName": "Oracle Cloud Success Protection Service - Universal Credits Consumption (% applied to consumption, with minimum)",
      "metricName": "Universal Credits Consumption",
      "serviceCategory": "Cloud Success Protection Service",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110470",
      "displayName": "Exadata Cloud@Customer - Database OCPU - Developer",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Database - Exadata Database - Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110475",
      "displayName": "OCI Fleet Application Management Service",
      "metricName": "1 Managed Resource Per Month",
      "serviceCategory": "Fleet Application Management",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 25
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 2.604,
              "rangeMin": 25,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110500",
      "displayName": "OCI Streaming With Apache Kafka",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "OCI Streaming With Apache Kafka",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110517",
      "displayName": "OCI Generative AI - Meta Llama 3.1 405B",
      "metricName": "10,000 Transactions",
      "serviceCategory": "OCI Generative AI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0267
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110519",
      "displayName": "OCI - Compute - GPU - H200",
      "metricName": "GPU Per Hour",
      "serviceCategory": "Compute - GPU",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 10
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110617",
      "displayName": "OCI - Vision - Stored Video Analysis",
      "metricName": "Processed Video Minute",
      "serviceCategory": "OCI Vision",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 1000
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1,
              "rangeMin": 1000,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110625",
      "displayName": "OCI - MySQL Database - Database Management for MySQL HeatWave",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "MySQL",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0075
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110627",
      "displayName": "Exadata Cloud Infrastructure - Database Server - X11M",
      "metricName": "Hosted Environment Per Hour",
      "serviceCategory": "Exadata Cloud Infrastructure X11M",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 2.9032
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110629",
      "displayName": "Exadata Cloud Infrastructure - Storage Server - X11M",
      "metricName": "Hosted Environment Per Hour",
      "serviceCategory": "Exadata Cloud Infrastructure X11M",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 2.9032
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110631",
      "displayName": "Exadata Database ECPU - Dedicated Infrastructure",
      "description": "You must add a minimum of 4 ECPUs per database server and ECPUs must be added in increments of 4.",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Exadata Database Service ECPUs",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.336
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110632",
      "displayName": "Exadata Database ECPU - Dedicated Infrastructure - BYOL",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Exadata Database Service ECPUs",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0807
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110662",
      "displayName": "Oracle Exadata Cloud@Customer Database ECPU - ECPU Per Hour",
      "description": "Min QTY 8",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Database - Exadata Database - Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.336
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110663",
      "displayName": "Oracle Exadata Cloud@Customer Database ECPU - BYOL",
      "description": "Min QTY 8",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Database - Exadata Database - Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0807
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110679",
      "displayName": "OCI Generative AI - Meta Llama 3.2 90B Vision",
      "metricName": "10,000 Transactions",
      "serviceCategory": "OCI Generative AI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.005
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110680",
      "displayName": "Oracle Cloud VMware Solution - BM.DenseIO.E5.32 - Hourly Commit",
      "metricName": "Node Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 10.2379
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110681",
      "displayName": "Oracle Cloud VMware Solution - BM.DenseIO.E5.32 - Monthly Commit",
      "metricName": "Node Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 8.531
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110682",
      "displayName": "Oracle Cloud VMware Solution - BM.DenseIO.E5.32 - 1 Year Commit",
      "metricName": "Node Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 6.8242
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110683",
      "displayName": "Oracle Cloud VMware Solution - BM.DenseIO.E5.32 - 3 Year Commit",
      "metricName": "Node Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 6.072
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110684",
      "displayName": "Oracle Cloud VMware Solution - Expansion - Monthly Commit",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1338
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110965",
      "displayName": "Oracle Compute Cloud@Customer - Compute - GPU.L40S",
      "metricName": "GPU Per Hour",
      "serviceCategory": "Compute Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 3.5
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110978",
      "displayName": "OCI - Compute - GPU - B200",
      "metricName": "GPU Per Hour",
      "serviceCategory": "Compute - GPU",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 14
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110979",
      "displayName": "OCI - Compute - GPU - GB200",
      "metricName": "GPU Per Hour",
      "serviceCategory": "Compute - GPU",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 16
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110986",
      "displayName": "OCI Application Performance Monitoring Service - Stack Monitoring - Enterprise Edition for GPU Infrastructure",
      "metricName": "GPU Monitoring Unit Per Hour",
      "serviceCategory": "Stack Monitoring - GPU",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.07
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110989",
      "displayName": "Oracle Globally Distributed Exadata Exascale Database ECPU",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Exadata Database Service ECPUs",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.3864
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110990",
      "displayName": "Oracle Globally Distributed Exadata Exascale Database ECPU - BYOL",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Exadata Database Service ECPUs",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0928
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B110993",
      "displayName": "Oracle Cloud Success Assurance Service - Universal Credits Consumption (% applied to consumption, with minimum)",
      "metricName": "Universal Credits Consumption",
      "serviceCategory": "Cloud Success Assurance Service",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.05
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B111015",
      "displayName": "OCI Generative AI - Cohere Rerank - Dedicated",
      "metricName": "Cluster Hour",
      "serviceCategory": "OCI Generative AI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 10
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B111035",
      "displayName": "OCI Generative AI - Meta Llama 4 Scout",
      "metricName": "10,000 Transactions",
      "serviceCategory": "OCI Generative AI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0018
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B111036",
      "displayName": "OCI Generative AI - Meta Llama 4 Maverick",
      "metricName": "10,000 Transactions",
      "serviceCategory": "OCI Generative AI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0018
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B111087",
      "displayName": "OCI Ops Insights for Warehouse - Instance",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Observability - Ops Insights",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.2688
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B111091",
      "displayName": "OCI File Storage with Lustre Service - Storage",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "File Storage with Lustre Service",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.086
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B111092",
      "displayName": "OCI File Storage with Lustre Service - Performance",
      "metricName": "Performance Units Per Gigabyte Per Month",
      "serviceCategory": "File Storage with Lustre Service",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0005
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B111129",
      "displayName": "OCI - Compute - Standard - E6 - OCPU",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.03
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B111130",
      "displayName": "OCI - Compute - Standard - E6 - Memory",
      "metricName": "Gigabyte Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.002
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88290",
      "displayName": "Oracle Database Cloud Service - Enterprise Edition - General Purpose",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Oracle Public Cloud Services - CLOUDCM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.4301
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88291",
      "displayName": "Oracle Database Cloud Service - Enterprise Edition Extreme Performance - General Purpose",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Oracle Public Cloud Services - CLOUDCM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.3441
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88292",
      "displayName": "Oracle Database Cloud Service - Enterprise Edition High Performance - General Purpose",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Oracle Public Cloud Services - CLOUDCM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.8871
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88293",
      "displayName": "Oracle Database Cloud Service - Standard Edition - General Purpose",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Oracle Public Cloud Services - CLOUDCM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.215
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88299",
      "displayName": "Oracle Data Integrator Cloud Service",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Data Integrator",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.7742
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88315",
      "displayName": "Compute - Bare Metal Standard - X5",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Bare Metal",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0638
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88317",
      "displayName": "Compute - Virtual Machine Standard - X5",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0638
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88318",
      "displayName": "Compute - Windows OS",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - OS Images",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.092
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88325",
      "displayName": "OCI - FastConnect 1 Gbps",
      "metricName": "Port Hour",
      "serviceCategory": "Networking - FastConnect",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.2125
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88326",
      "displayName": "OCI - FastConnect 10 Gbps",
      "metricName": "Port Hour",
      "serviceCategory": "Networking - FastConnect",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.275
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88327",
      "displayName": "Outbound Data Transfer - Originating in North America, Europe, and UK",
      "metricName": "Gigabyte Outbound Data Transfer Per Month",
      "serviceCategory": "Networking - Virtual Cloud Networks",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 10240
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0085,
              "rangeMin": 10240,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88402",
      "displayName": "Oracle Database Cloud Service - Enterprise Edition Extreme Performance RAC - BYOL",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Oracle Public Cloud Services - CLOUDCM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1935
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88404",
      "displayName": "Oracle Database Cloud Service - All Editions - BYOL",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Oracle Public Cloud Services - CLOUDCM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1935
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88406",
      "displayName": "Oracle Data Integrator Cloud Service - BYOL",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Data Integrator",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1935
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88513",
      "displayName": "Compute - Bare Metal Standard - X7",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Bare Metal",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0638
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88514",
      "displayName": "Compute - Virtual Machine Standard - X7",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0638
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88515",
      "displayName": "Compute - Bare Metal Dense I/O - X7",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Bare Metal",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1275
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88516",
      "displayName": "Compute - Virtual Machine Dense I/O - X7",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1275
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88517",
      "displayName": "Compute - Bare Metal GPU Standard - X7",
      "metricName": "GPU Per Hour",
      "serviceCategory": "Compute - GPU",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.275
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88518",
      "displayName": "Compute - Virtual Machine GPU Standard - X7",
      "metricName": "GPU Per Hour",
      "serviceCategory": "Compute - GPU",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.275
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88523",
      "displayName": "OCI - Email Delivery",
      "metricName": "1,000 Emails Sent",
      "serviceCategory": "Email Delivery",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 3
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.085,
              "rangeMin": 3,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88525",
      "displayName": "Networking - DNS",
      "metricName": "1,000,000 Queries",
      "serviceCategory": "Networking - DNS",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.85
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88592",
      "displayName": "Exadata Database OCPU - Dedicated Infrastructure",
      "description": "You must add a minimum of 2 OCPUs per database server and OCPUs must be added in increments of 2.",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Exadata Database Service OCPUs",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.3441
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B88847",
      "displayName": "Exadata Database OCPU - Dedicated Infrastructure - BYOL",
      "description": "You must add a minimum of 2 OCPUs per database server and OCPUs must be added in increments of 2.",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Exadata Database Service OCPUs",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.3226
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B89057",
      "displayName": "File Storage - Storage",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "Storage - File Storage",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.3
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B89630",
      "displayName": "Oracle Analytics Cloud - Professional - OCPU",
      "description": "If you stop/pause the service, metering will still continue at fifteen percent (15%) of your OCPU Per Hour rate while it is stopped/paused. When the service is started/resumed the service will meter at your OCPU Per Hour rate.",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Analytics - Analytics Cloud",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.0753
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B89631",
      "displayName": "Oracle Analytics Cloud - Enterprise - OCPU",
      "description": "If you stop/pause the service, metering will still continue at fifteen percent (15%) of your OCPU Per Hour rate while it is stopped/paused. When the service is started/resumed the service will meter at your OCPU Per Hour rate.",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Analytics - Analytics Cloud",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 2.1506
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B89636",
      "displayName": "Oracle Analytics Cloud - Professional - BYOL - OCPU",
      "description": "If you stop/pause the service, metering will still continue at fifteen percent (15%) of your OCPU Per Hour rate while it is stopped/paused. When the service is started/resumed the service will meter at your OCPU Per Hour rate.",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Analytics - Analytics Cloud",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.3226
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B89637",
      "displayName": "Oracle Analytics Cloud - Enterprise - BYOL - OCPU",
      "description": "If you stop/pause the service, metering will still continue at fifteen percent (15%) of your OCPU Per Hour rate while it is stopped/paused. When the service is started/resumed the service will meter at your OCPU Per Hour rate.",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Analytics - Analytics Cloud",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.3226
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B89639",
      "displayName": "Oracle Integration Cloud Service - Standard",
      "metricName": "5K Messages Per Hour",
      "serviceCategory": "Application Integration - OIC",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.6452
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B89640",
      "displayName": "Oracle Integration Cloud Service - Enterprise",
      "metricName": "5K Messages Per Hour",
      "serviceCategory": "Application Integration - OIC",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.2903
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B89643",
      "displayName": "Oracle Integration Cloud Service - Standard - BYOL",
      "metricName": "20K Messages Per Hour",
      "serviceCategory": "Application Integration - OIC",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.3226
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B89644",
      "displayName": "Oracle Integration Cloud Service - Enterprise - BYOL",
      "metricName": "20K Messages Per Hour",
      "serviceCategory": "Application Integration - OIC",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.3226
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B89646",
      "displayName": "Oracle Visual Builder",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Application Development - Developer Cloud Service",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.2365
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B89734",
      "displayName": "Compute - GPU Standard - V2",
      "metricName": "GPU Per Hour",
      "serviceCategory": "Compute - GPU",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 2.95
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B89737",
      "displayName": "Oracle NoSQL Database Cloud - Write",
      "metricName": "Write Unit Per Month",
      "serviceCategory": "Database - NoSQL",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1254
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B89738",
      "displayName": "Oracle NoSQL Database Cloud - Read",
      "metricName": "Read Unit Per Month",
      "serviceCategory": "Database - NoSQL",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0064
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B89739",
      "displayName": "Oracle NoSQL Database Cloud - Storage",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "Database - NoSQL",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.066
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B89980",
      "displayName": "Oracle Database Exadata Cloud at Customer - Database OCPU",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Database - Exadata Database - Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.3441
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B89981",
      "displayName": "Oracle Database Exadata Cloud at Customer - Database OCPU - BYOL",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Database - Exadata Database - Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.3226
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B89999",
      "displayName": "Quarter Rack - X7",
      "metricName": "Hosted Environment Per Hour",
      "serviceCategory": "Exadata Cloud Infrastructure X7",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 21.5054
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90000",
      "displayName": "Half Rack - X7",
      "metricName": "Hosted Environment Per Hour",
      "serviceCategory": "Exadata Cloud Infrastructure X7",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 43.0107
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90001",
      "displayName": "Full Rack - X7",
      "metricName": "Hosted Environment Per Hour",
      "serviceCategory": "Exadata Cloud Infrastructure X7",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 86.0215
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90203",
      "displayName": "Visual Builder Studio - Additional Storage",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "Application Development - Developer Cloud Service",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.6
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90230",
      "displayName": "Oracle Database Backup Cloud - Object Storage",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "Database - Backup",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0051
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90231",
      "displayName": "Oracle Database Backup Cloud - Archive Storage",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "Database - Backup",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0005
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90260",
      "displayName": "Oracle Digital Assistant Cloud Service",
      "description": "For the purposes of the Oracle Digital Assistant Cloud Service, each customer Cloud Services Account consumes a minimum of 250 Requests Per Hour, which includes 1 development and 1 production environment.\nAn additional development environment consumes a minimum of an additional 50 Requests Per Hour. An additional production environment consumes a minimum of an additional 200 Requests Per Hour.",
      "metricName": "Requests per Hour",
      "serviceCategory": "Oracle Digital Assistant",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0232
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90304",
      "displayName": "Oracle Mobile Hub Cloud Service",
      "metricName": "Requests per Hour",
      "serviceCategory": "Mobile",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0028
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90323",
      "displayName": "OCI - Health Checks - Basic",
      "metricName": "Endpoints Per Month",
      "serviceCategory": "Edge Services",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.3
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90325",
      "displayName": "OCI - Health Checks - Premium",
      "metricName": "Endpoints Per Month",
      "serviceCategory": "Edge Services",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.3
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90327",
      "displayName": "Networking - DNS Traffic Management",
      "metricName": "1,000,000 DNS Traffic Managment Queries",
      "serviceCategory": "Networking - DNS",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 4
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90328",
      "displayName": "OCI - Key Management - Private Vault",
      "metricName": "Virtual Private Vault Per Hour",
      "serviceCategory": "Security - Key Management",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 3.724
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90398",
      "displayName": "Compute - Bare Metal Standard - HPC - X7",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Bare Metal",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.075
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90425",
      "displayName": "Compute - Standard - E2",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.03
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90555",
      "displayName": "Oracle Identity Cloud Service - Enterprise User",
      "metricName": "User Per Month",
      "serviceCategory": "OCI IAM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 3.2
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90556",
      "displayName": "Oracle Identity Cloud Service - Consumer User",
      "metricName": "User Per Month",
      "serviceCategory": "OCI IAM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.016
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90557",
      "displayName": "Oracle Identity Cloud Service - Enterprise User - BYOL",
      "metricName": "User Per Month",
      "serviceCategory": "OCI IAM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.8
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90558",
      "displayName": "Oracle Identity Cloud Service - Consumer User - BYOL",
      "metricName": "User Per Month",
      "serviceCategory": "OCI IAM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.004
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90569",
      "displayName": "Oracle Base Database Service - Standard",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Database - Base Database Service - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.215
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90570",
      "displayName": "Oracle Base Database Service - Enterprise",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Database - Base Database Service - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.4301
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90571",
      "displayName": "Oracle Base Database Service - High Performance",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Database - Base Database Service - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.8871
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90572",
      "displayName": "Oracle Base Database Service - Extreme Performance",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Database - Base Database Service - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.3441
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90573",
      "displayName": "Oracle Base Database Service - BYOL",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Database - Base Database Service - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1935
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90617",
      "displayName": "Oracle Functions - Execution Time",
      "metricName": "10,000 GB Memory-Seconds",
      "serviceCategory": "Application Development - Serverless",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 40
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1417,
              "rangeMin": 40,
              "rangeMax": 999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90618",
      "displayName": "Oracle Functions - Invocations",
      "metricName": "1MIL Function Invocations",
      "serviceCategory": "Application Development - Serverless",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 2
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.2,
              "rangeMin": 2,
              "rangeMax": 999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90777",
      "displayName": "Exadata Database Cloud Service - Base System",
      "metricName": "Hosted Environment Per Hour",
      "serviceCategory": "Exadata Cloud Infrastructure Base System",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 10.7527
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90925",
      "displayName": "Monitoring - Ingestion",
      "metricName": "Million Datapoints",
      "serviceCategory": "Observability - Monitoring",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 500
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0025,
              "rangeMin": 500,
              "rangeMax": 999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90926",
      "displayName": "Monitoring - Retrieval",
      "metricName": "Million Datapoints",
      "serviceCategory": "Observability - Monitoring",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 1000
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0015,
              "rangeMin": 1000,
              "rangeMax": 999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90936",
      "displayName": "Oracle Identity Foundation Cloud Service",
      "metricName": "Each",
      "serviceCategory": "OCI IAM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90938",
      "displayName": "Streaming - PUT or GET",
      "metricName": "Gigabytes of Data Transferred",
      "serviceCategory": "OCI Streaming",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.025
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90939",
      "displayName": "Streaming - Storage",
      "metricName": "Gigabyte Per Hour",
      "serviceCategory": "OCI Streaming",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0002
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90940",
      "displayName": "Notifications - HTTPS Delivery",
      "metricName": "Million Delivery Operations",
      "serviceCategory": "Observability - Notifications",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 1
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.6,
              "rangeMin": 1,
              "rangeMax": 999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B90941",
      "displayName": "Notifications - Email Delivery",
      "metricName": "1,000 Emails Sent",
      "serviceCategory": "Observability - Notifications",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 1
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.02,
              "rangeMin": 1,
              "rangeMax": 999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91119",
      "displayName": "Compute - Bare Metal Standard - B1",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Bare Metal",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0638
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91120",
      "displayName": "Compute - Virtual Machine Standard - B1",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0638
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91121",
      "displayName": "Oracle Cloud SQL - Compute Capacity",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Data Management - Big Data Cloud Service",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1075
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91128",
      "displayName": "Oracle Big Data Service - Compute - Standard",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Data Management - Big Data Cloud Service",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1344
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91129",
      "displayName": "Oracle Big Data Service - Compute - Dense I/O",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Data Management - Big Data Cloud Service",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.214
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91130",
      "displayName": "Oracle Big Data Service - Compute - HPC",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Data Management - Big Data Cloud Service",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1536
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91346",
      "displayName": "WebLogic Server Enterprise Edition for OCI",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Application Development - Java",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.2581
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91347",
      "displayName": "WebLogic Server Suite for OCI",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Application Development - Java",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.4646
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91363",
      "displayName": "Gen 2 Exadata Cloud at Customer - Database OCPU",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Database - Exadata Database - Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.3441
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91364",
      "displayName": "Gen 2 Exadata Cloud at Customer - Database OCPU - BYOL",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Database - Exadata Database - Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.3226
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91372",
      "displayName": "Database - Marketplace Compute Image - Microsoft SQL Enterprise",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Database - SQL Server",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.47
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91373",
      "displayName": "Database - Marketplace Compute Image - Microsoft SQL Standard",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Database - SQL Server",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.37
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91391",
      "displayName": "Oracle Autonomous Data Warehouse - Free",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Oracle Public Cloud Services - CLOUDCM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91392",
      "displayName": "Oracle Autonomous Data Warehouse - Exadata Storage - Free",
      "metricName": "Terabyte Storage Capacity Per Month",
      "serviceCategory": "Oracle Public Cloud Services - CLOUDCM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91393",
      "displayName": "Oracle Autonomous Transaction Processing - Free",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Oracle Public Cloud Services - CLOUDCM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91394",
      "displayName": "Oracle Autonomous Transaction Processing - Exadata Storage - Free",
      "metricName": "Terabyte Storage Capacity Per Month",
      "serviceCategory": "Oracle Public Cloud Services - CLOUDCM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91444",
      "displayName": "Compute - Virtual Machine Standard - E2 Micro - Free",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91445",
      "displayName": "Storage - Block Volume - Free 200GB",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "Storage - Block Volumes",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91535",
      "displayName": "Quarter Rack - X8",
      "metricName": "Hosted Environment Per Hour",
      "serviceCategory": "Exadata Cloud Infrastructure X8",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 14.5162
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91536",
      "displayName": "Half Rack - X8",
      "metricName": "Hosted Environment Per Hour",
      "serviceCategory": "Exadata Cloud Infrastructure X8",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 29.0323
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91537",
      "displayName": "Full Rack - X8",
      "metricName": "Hosted Environment Per Hour",
      "serviceCategory": "Exadata Cloud Infrastructure X8",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 58.0645
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91627",
      "displayName": "Object Storage - Requests",
      "description": "Example: Qty 8 = 80,000 Requests",
      "metricName": "10,000 Requests per Month (first 50,000 free)",
      "serviceCategory": "Storage - Object Storage",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 5
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0034,
              "rangeMin": 5,
              "rangeMax": 999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91628",
      "displayName": "Object Storage - Storage",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "Storage - Object Storage",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 10
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0255,
              "rangeMin": 10,
              "rangeMax": 999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91631",
      "displayName": "Data Safe for Database Cloud Service - Audit Record Collection Over 1 Million Records",
      "description": "Over the included 1,000,000 Audit Records Per Month",
      "metricName": "10,000 Audit Records Per Target Per Month",
      "serviceCategory": "Security - Data Safe",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91632",
      "displayName": "Data Safe for Database Cloud Service - Databases",
      "description": "Includes 1,000,000 Audit Records Per Target Per Month",
      "metricName": "Each",
      "serviceCategory": "Security - Data Safe",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91633",
      "displayName": "Archive Storage - Storage",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "Storage - Object Storage",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 10
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0026,
              "rangeMin": 10,
              "rangeMax": 999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91961",
      "displayName": "Storage - Block Volume - Storage",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "Storage - Block Volumes",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0255
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B91962",
      "displayName": "Storage - Block Volume - Performance Units",
      "metricName": "Performance Units Per Gigabyte Per Month",
      "serviceCategory": "Storage - Block Volumes",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0017
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92072",
      "displayName": "API Gateway  - 1,000,000 API Calls",
      "metricName": "1,000,000 API Calls Per Month",
      "serviceCategory": "Application Development - API Management",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 3
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92092",
      "displayName": "Key Management Service - Key Versions",
      "metricName": "Key Version Per Month",
      "serviceCategory": "Security - Key Management",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 20
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.5334,
              "rangeMin": 20,
              "rangeMax": 999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92302",
      "displayName": "Blockchain Platform - Standard",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Application Development - Blockchain",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.215
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92303",
      "displayName": "Blockchain Platform - Enterprise",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Application Development - Blockchain",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.4301
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92304",
      "displayName": "Blockchain Platform - Storage",
      "metricName": "Terabyte Storage Capacity Per Month",
      "serviceCategory": "Application Development - Blockchain",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 70.4
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92305",
      "displayName": "Blockchain Platform - Enterprise - BYOL",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Application Development - Blockchain",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.3226
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92306",
      "displayName": "Compute - Standard - E3 - OCPU",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.025
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92307",
      "displayName": "Compute - Standard - E3 - Memory",
      "metricName": "Gigabyte Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0015
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92335",
      "displayName": "Essbase for OCI",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "OCI Marketplace - Database - Essbase on OCI",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.3129
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92380",
      "displayName": "Exadata Database Cloud Service - Quarter Rack  - X8M",
      "description": "The initial Exadata X8M infrastructure configuration is a Quarter Rack which includes 2 Database Servers and 3 Storage Servers. You can elastically scale the infrastructure by incrementally adding database and/or storage servers up to a total of 32 Database Servers and 64 Storage Servers within a single Exadata Cloud Service.  OCPUs are provisioned separately",
      "metricName": "Hosted Environment Per Hour",
      "serviceCategory": "Exadata Cloud Infrastructure X8M",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 14.5162
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92381",
      "displayName": "Exadata Database Cloud Service - Database Server - X8M",
      "description": "Each Database Server includes 1,390 GB of memory and supports enabling up to 50 OCPUs per Database Server.     Best practice is to enable a minimum of 2 OCPU per Database Server and scale up/down from that point as appropriate in increments of 2 per Database Server.   For example, if your service includes a total of 4 Database Servers, provision a minimum of 8 OCPUs then scale OCPUs up or down in multiples of 8 (2 OCPUs x 4 Database Servers).",
      "metricName": "Hosted Environment Per Hour",
      "serviceCategory": "Exadata Cloud Infrastructure X8M",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 2.9032
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92382",
      "displayName": "Exadata Database Cloud Service - Storage Server - X8M",
      "description": "Each Storage Server includes 1.5 TB of Persistent Memory (PMEM), 25.6 TB of PCI NVMe Flash, and 49.9 TB of usable disk capacity.",
      "metricName": "Hosted Environment Per Hour",
      "serviceCategory": "Exadata Cloud Infrastructure X8M",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 2.9032
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92386",
      "displayName": "Oracle Cloud Vmware Solution  - BM.DenseIO2.52 - 1 Month Commit",
      "description": "Minimum duration: 1 Month. Minimum configuration: 3 BM hosts, 52 OCPUs each, total 156 OCPUs. Additional hosts with 52 OCPUs each can be added.",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.2031
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92426",
      "displayName": "MySQL Database - Storage",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "MySQL",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.04
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92450",
      "displayName": "Oracle SOA Suite for OCI",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Application Integration - SOA",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.7231
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92451",
      "displayName": "Oracle SOA Suite for OCI - with B2B Adapter for EDI",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Application Integration - SOA",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.2071
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92483",
      "displayName": "MySQL Database - Backup Storage",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "MySQL",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.04
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92593",
      "displayName": "OCI - Logging - Storage",
      "metricName": "Gigabyte Log Storage Per Month",
      "serviceCategory": "Observability - Logging",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 10
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.05,
              "rangeMin": 10,
              "rangeMax": 999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92598",
      "displayName": "OCI Data Integration - Workspace Usage",
      "metricName": "Workspace Usage Per Hour",
      "serviceCategory": "Data Integration - Workspace Usage",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.16
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92599",
      "displayName": "OCI Data Integration - Data Processed",
      "metricName": "Gigabyte of Data Processed Per Hour",
      "serviceCategory": "Data Integration - Data Processed",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.04
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92615",
      "displayName": "Roving Edge Device - Compute Optimized - Ruggedized",
      "metricName": "Resource Possession Per Day",
      "serviceCategory": "Roving Edge Infrastructure",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 160
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92627",
      "displayName": "Oracle NoSQL Database Cloud - Write - Free",
      "description": "Free is subject to the following quantities: 50 Write Units per table per month. A maximum of 3 tables can be allocated with the Free Tier.",
      "metricName": "Write Unit Per Month",
      "serviceCategory": "Database - NoSQL",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92628",
      "displayName": "Oracle NoSQL Database Cloud - Read - Free",
      "description": "Free is subject to the following quantities: 50 Read Units per table per month. A maximum of 3 tables can be allocated with the Free Tier.",
      "metricName": "Read Unit Per Month",
      "serviceCategory": "Database - NoSQL",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92629",
      "displayName": "Oracle NoSQL Database Cloud - Storage - Free",
      "description": "Free is subject to the following quantities: 25 Gigabyte (GB) storage capacity per table. A maximum of 3 tables can be allocated with the Free Tier.",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "Database - NoSQL",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92682",
      "displayName": "Oracle Analytics Cloud - Professional - Users",
      "description": "Min 10 Users Per Month",
      "metricName": "User Per Month",
      "serviceCategory": "Analytics - Analytics Cloud",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 16
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92683",
      "displayName": "Oracle Analytics Cloud - Enterprise - Users",
      "description": "Min 10 Users Per Month",
      "metricName": "User Per Month",
      "serviceCategory": "Analytics - Analytics Cloud",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 80
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92695",
      "displayName": "Oracle Stream Analytics for OCI",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "GoldenGate",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.9498
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92733",
      "displayName": "Data Safe for On-Premises Databases          on Compute",
      "description": "Includes 1,000,000 Audit Records Per Target Per Month",
      "metricName": "Target Database Per Month",
      "serviceCategory": "Security - Data Safe",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 200,
              "rangeMin": 0,
              "rangeMax": 100
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 150,
              "rangeMin": 100,
              "rangeMax": 300
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 100,
              "rangeMin": 300,
              "rangeMax": 500
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 50,
              "rangeMin": 500,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92734",
      "displayName": "Data Safe for On-Premises Databases prompt on Compute",
      "description": "Over the included 1,000,000 Audit Records Per Month",
      "metricName": "10,000 Audit Records Per Target Per Month",
      "serviceCategory": "Security - Data Safe",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92740",
      "displayName": "Compute - GPU - E3",
      "metricName": "GPU Per Hour",
      "serviceCategory": "Compute - GPU",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 3.05
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92809",
      "displayName": "Logging Analytics - Archival Storage",
      "metricName": "Logging Analytics Storage Unit Per Hour",
      "serviceCategory": "Observability - Logging Analytics",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.02
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92888",
      "displayName": "OCI Ops Insights for Oracle Autonomous Databases - Basic",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Observability - Ops Insights",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92889",
      "displayName": "OCI Ops Insights for Oracle Cloud Databases",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Observability - Ops Insights",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.015
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92890",
      "displayName": "OCI Ops Insights for External Oracle Databases and Host",
      "metricName": "Host CPU Core Per Hour",
      "serviceCategory": "Observability - Ops Insights",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.015
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92913",
      "displayName": "WebLogic Server Enterprise Edition for OCI Container Engine for Kubernetes",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Application Development - Java",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.2581
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92914",
      "displayName": "WebLogic Server Suite for OCI Container Engine for Kubernetes",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Application Development - Java",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.4646
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92940",
      "displayName": "Application Performance Monitoring Service - Tracing Data - Free",
      "description": "Always Free, limited to 1000 events per hour.",
      "metricName": "1,000 Events Per Hour",
      "serviceCategory": "Observability - APM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92941",
      "displayName": "Application Performance Monitoring Service - Tracing Data",
      "metricName": "100,000 Events Per Hour",
      "serviceCategory": "Observability - APM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.65
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92942",
      "displayName": "Application Performance Monitoring Service -  Synthetic Usage",
      "description": "A minimum of one unit of Application Performance Monitoring Service - Tracing Data (B92941) is required with this service",
      "metricName": "10 Monitor Runs Per Hour",
      "serviceCategory": "Observability - APM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.02
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92992",
      "displayName": "OCI GoldenGate",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "GoldenGate",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.3441
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B92993",
      "displayName": "OCI GoldenGate - BYOL",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "GoldenGate",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.3226
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93000",
      "displayName": "Infrequent Access Storage - Storage",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "Storage - Object Storage",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 10
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.01,
              "rangeMin": 10,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93001",
      "displayName": "Infrequent Access Storage - Data Retrieval",
      "metricName": "GB Storage Retrieved Per Month",
      "serviceCategory": "Storage - Object Storage",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 10
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.01,
              "rangeMin": 10,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93004",
      "displayName": "Notifications - SMS Outbound to Country Zone 1",
      "metricName": "Per SMS Message Sent",
      "serviceCategory": "Observability - Notifications",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 100
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.015,
              "rangeMin": 100,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93005",
      "displayName": "Notifications - SMS Outbound to Country Zone 2",
      "metricName": "Per SMS Message Sent",
      "serviceCategory": "Observability - Notifications",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 100
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.045,
              "rangeMin": 100,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93006",
      "displayName": "Notifications - SMS Outbound to Country Zone 3",
      "metricName": "Per SMS Message Sent",
      "serviceCategory": "Observability - Notifications",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 100
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.086,
              "rangeMin": 100,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93007",
      "displayName": "Notifications - SMS Outbound to Country Zone 4",
      "metricName": "Per SMS Message Sent",
      "serviceCategory": "Observability - Notifications",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 100
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.12,
              "rangeMin": 100,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93008",
      "displayName": "Notifications - SMS Outbound to Country Zone 5",
      "metricName": "Per SMS Message Sent",
      "serviceCategory": "Observability - Notifications",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 100
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.24,
              "rangeMin": 100,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93030",
      "displayName": "Load Balancer Base",
      "description": "Enter number of Flexible Load Balancer instances. Each month, the first Flexible Load Balancer instance is free. NOTE: OCI Flexible Network Load Balancer is offered at no cost.",
      "metricName": "Load Balancer",
      "serviceCategory": "Flexible Load Balancer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 744
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0113,
              "rangeMin": 744,
              "rangeMax": 999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93031",
      "displayName": "Load Balancer Bandwidth",
      "description": "Enter estimated cumulative bandwidth for all Load Balancer instances.\nEach month, the first 10 Mbps per hour is free.",
      "metricName": "Mbps Per Hour",
      "serviceCategory": "Flexible Load Balancer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 7440
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0001,
              "rangeMin": 7440,
              "rangeMax": 999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93039",
      "displayName": "Roving Edge Device - Compute Optimized - Standard",
      "metricName": "Resource Possession Per Day",
      "serviceCategory": "Roving Edge Infrastructure",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 160
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93040",
      "displayName": "Roving Edge Device - Compute Optimized - Unreturnable or Loss Fee",
      "metricName": "Each",
      "serviceCategory": "Roving Edge Infrastructure",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 45000
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93082",
      "displayName": "Database Management - External DB BYOL",
      "metricName": "Host CPU Core Per Hour",
      "serviceCategory": "Oracle Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.025
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93083",
      "displayName": "Database Management - External DB",
      "metricName": "Host CPU Core Per Hour",
      "serviceCategory": "Oracle Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.05
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93113",
      "displayName": "Compute - Standard - E4 - OCPU",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.025
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93114",
      "displayName": "Compute - Standard - E4  - Memory",
      "metricName": "Gigabyte Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0015
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93121",
      "displayName": "Compute - Dense I/O - E4 - OCPU",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.025
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93122",
      "displayName": "Compute - Dense I/O - E4 - Memory",
      "metricName": "Gigabyte Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0015
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93123",
      "displayName": "Compute - Dense I/O - E4 - NVMe",
      "metricName": "NVMe Terabyte Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0612
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93126",
      "displayName": "OCI - FastConnect 100 Gbps",
      "metricName": "Port Hour",
      "serviceCategory": "Networking - FastConnect",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 10.75
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93199",
      "displayName": "OCI Database Migration",
      "metricName": "Migration Hour",
      "serviceCategory": "Database - Database Migration",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.2
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93288",
      "displayName": "Oracle Cloud VMware Solution - BM.DenseIO2.52 - Hourly Commit",
      "description": "Minimum duration: 8 hours. Minimum configuration: 3 BM hosts, 52 OCPUs each, total 156 OCPUs. Additional hosts with 52 OCPUs each can be added.",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.2437
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93289",
      "displayName": "Oracle Cloud VMware Solution - BM.DenseIO2.52 - 1 Year Commit",
      "description": "Minimum duration: 1 Year. Minimum configuration: 3 BM hosts, 52 OCPUs each, total 156 OCPUs. Additional hosts with 52 OCPUs each can be added.",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1625
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93290",
      "displayName": "Oracle Cloud VMware Solution - BM.DenseIO2.52 - 3 Year Commit",
      "description": "Minimum duration: 3 Years. Minimum configuration: 3 BM hosts, 52 OCPUs each, total 156 OCPUs. Additional hosts with 52 OCPUs each can be added.",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.132
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93297",
      "displayName": "Compute - Standard - A1 - OCPU",
      "description": "In Oracle's ARM compute shapes, OCPUs equate to vCPUs.",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 3000
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.01,
              "rangeMin": 3000,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93298",
      "displayName": "Compute - Standard - A1 - Memory",
      "metricName": "Gigabyte Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 18000
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0015,
              "rangeMin": 18000,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93306",
      "displayName": "OCI Data Integration - Pipeline Operator Execution",
      "metricName": "Execution Hour",
      "serviceCategory": "Data Integration - Pipeline Operator Execution",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 30
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.3,
              "rangeMin": 30,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93307",
      "displayName": "Autonomous JSON Database - Free",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Database - Autonomous JSON Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93311",
      "displayName": "Compute - Optimized - X9 - OCPU",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.054
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93312",
      "displayName": "Compute - Optimized - X9 - Memory",
      "metricName": "Gigabyte Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0015
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93320",
      "displayName": "Oracle APEX Application Development - Free",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "APEX Application Development",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93380",
      "displayName": "Exadata Cloud Infrastructure - Quarter Rack - X9M",
      "metricName": "Hosted Environment Per Hour",
      "serviceCategory": "Exadata Cloud Infrastructure X9M",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 14.5162
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93381",
      "displayName": "Exadata Cloud Infrastructure - Database Server - X9M",
      "metricName": "Hosted Environment Per Hour",
      "serviceCategory": "Exadata Cloud Infrastructure X9M",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 2.9032
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93382",
      "displayName": "Exadata Cloud Infrastructure - Storage Server - X9M",
      "metricName": "Hosted Environment Per Hour",
      "serviceCategory": "Exadata Cloud Infrastructure X9M",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 2.9032
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93421",
      "displayName": "Oracle Cloud VMware Solution - HCX Enterprise - Monthly",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0126
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93423",
      "displayName": "OCI - AI Services - Language - Pre-trained Inferencing",
      "description": "Enter a value in block of 1000 Transactions, round up to nearest 1000. eg. Enter 8 for 8000 Transactions.  (1 Transaction equals 1 API detect call)\nEach month, first 5000 Transactions(ie 5 blocks of 1000 Transactions) are free.",
      "metricName": "1,000 Transactions",
      "serviceCategory": "OCI Language",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 5
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.25,
              "rangeMin": 5,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93426",
      "displayName": "Database Management - Cloud Databases",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Oracle Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.05
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93455",
      "displayName": "Outbound Data Transfer - Originating in APAC, Japan, and South America",
      "metricName": "Gigabyte Outbound Data Transfer Per Month",
      "serviceCategory": "Networking - Virtual Cloud Networks",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 10240
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.025,
              "rangeMin": 10240,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93456",
      "displayName": "Outbound Data Transfer - Originating in Middle East and Africa",
      "metricName": "Gigabyte Outbound Data Transfer Per Month",
      "serviceCategory": "Networking - Virtual Cloud Networks",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 10240
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.05,
              "rangeMin": 10240,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93493",
      "displayName": "Identity and Access Management - External User",
      "metricName": "User Per Month",
      "serviceCategory": "OCI IAM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.016
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93494",
      "displayName": "Identity and Access Management - Oracle Apps Premium",
      "metricName": "User Per Month",
      "serviceCategory": "OCI IAM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.25
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93495",
      "displayName": "Identity and Access Management - Premium",
      "metricName": "User Per Month",
      "serviceCategory": "OCI IAM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 3.2
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93496",
      "displayName": "Identity and Access Management - SMS",
      "metricName": "1 SMS Message Sent",
      "serviceCategory": "OCI IAM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 1000
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.03,
              "rangeMin": 1000,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93497",
      "displayName": "Identity and Access Management - Token",
      "metricName": "Token",
      "serviceCategory": "OCI IAM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.004,
              "rangeMin": 10000,
              "rangeMax": 999999999999999
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 10000
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93498",
      "displayName": "Identity and Access Management - Replication",
      "metricName": "User Per Month",
      "serviceCategory": "OCI IAM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.004
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93555",
      "displayName": "Oracle Big Data Service",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Big Data Service",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.015
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93705",
      "displayName": "OCI Ops Insights for Warehouse - Extract",
      "metricName": "Gigabyte Per Month",
      "serviceCategory": "Observability - Ops Insights",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 2
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93706",
      "displayName": "OCI Ops Insights for Warehouse - Instance",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Observability - Ops Insights",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.5377
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93709",
      "displayName": "OCI Search with OpenSearch HA",
      "description": "Select 1 for an HA cluster with 3 data nodes.  Note: Include Compute VM, Memory, Block and Object storage for a complete estimate",
      "metricName": "Node Per Hour",
      "serviceCategory": "Search Service with OpenSearch",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.25
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93710",
      "displayName": "Oracle NoSQL Database Cloud - Write - Auto",
      "metricName": "Write Unit Per Month",
      "serviceCategory": "Database - NoSQL",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 3.135
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93711",
      "displayName": "Oracle NoSQL Database Cloud - Read - Auto",
      "metricName": "Read Unit Per Month",
      "serviceCategory": "Database - NoSQL",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.16
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B93712",
      "displayName": "Oracle NoSQL Database Cloud - Hosted Environment",
      "metricName": "Hosted Environment Per Month",
      "serviceCategory": "Database - NoSQL",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 28796
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B94173",
      "displayName": "Oracle Threat Intelligence Service",
      "metricName": "API Calls",
      "serviceCategory": "Security - Threat Intelligence",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B94176",
      "displayName": "Compute - Standard - X9 - OCPU",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.04
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B94177",
      "displayName": "Compute - Standard - X9 - Memory",
      "metricName": "Gigabyte Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0015
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B94277",
      "displayName": "Web Application Firewall - Requests",
      "metricName": "1,000,000 Incoming Requests Per Month",
      "serviceCategory": "WAF",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 10
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.6,
              "rangeMin": 10,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B94282",
      "displayName": "Data Labeling",
      "metricName": "Annotated Data Record",
      "serviceCategory": "OCI Data Labeling",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 1000
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0002,
              "rangeMin": 1000,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B94568",
      "displayName": "Oracle Analytics Server for OCI",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Analytics",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.75
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B94579",
      "displayName": "Web Application Firewall - Instance",
      "metricName": "Instance Per Month",
      "serviceCategory": "WAF",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 1
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 5,
              "rangeMin": 1,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B94896",
      "displayName": "Speech",
      "description": "Enter number of hours to be transcribed.  Actual billing is done on a per second basis.",
      "metricName": "Transcription Hour",
      "serviceCategory": "OCI Speech",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 5
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.35,
              "rangeMin": 5,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B94973",
      "displayName": "Vision - Image Analysis",
      "description": "Detect objects or classify images (pretrained and custom)",
      "metricName": "1,000 Transactions",
      "serviceCategory": "OCI Vision",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 5
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.25,
              "rangeMin": 5,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B94974",
      "displayName": "Vision - OCR",
      "description": "Extract text from images and documents (pretrained)",
      "metricName": "1,000 Transactions",
      "serviceCategory": "OCI Vision",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 5
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1,
              "rangeMin": 5,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B94977",
      "displayName": "Vision - Custom Training",
      "description": "Training hours used to train a custom model",
      "metricName": "Training Hour",
      "serviceCategory": "OCI Vision",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.5,
              "rangeMin": 15,
              "rangeMax": 999999999999999
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 15
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95178",
      "displayName": "Oracle Cloud VMware Solution - BM.DenseIO.E4.64 - Hourly Commit",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.2437
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95179",
      "displayName": "Oracle Cloud VMware Solution - BM.DenseIO.E4.64 - Monthly Commit",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.2031
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95180",
      "displayName": "Oracle Cloud VMware Solution - BM.DenseIO.E4.64 - 1 year Commit",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1625
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95181",
      "displayName": "Oracle Cloud VMware Solution - BM.DenseIO.E4.64 - 3 year Commit",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.132
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95228",
      "displayName": "Roving Edge Ultra",
      "metricName": "Resource Possession Per Day",
      "serviceCategory": "Roving Edge Infrastructure",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 45
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95229",
      "displayName": "Roving Edge Ultra Non-Return or Loss Fee",
      "metricName": "Each",
      "serviceCategory": "Roving Edge Infrastructure",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 35000
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95240",
      "displayName": "Oracle Database Autonomous Recovery Service",
      "metricName": "Virtualized GB Per Month",
      "serviceCategory": "Oracle Database Autonomous Recovery Service",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0306
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95241",
      "displayName": "Oracle Database Zero Data Loss Autonomous Recovery Service",
      "metricName": "Virtualized GB Per Month",
      "serviceCategory": "Oracle Database Autonomous Recovery Service",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.04
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95264",
      "displayName": "OCI - Application Performance Monitoring Service - Stack Monitoring - Standard Edition",
      "metricName": "10 Monitored Resources Per Hour",
      "serviceCategory": "Observability - APM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.075
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95279",
      "displayName": "Media Services - Media Flow - Standard - H264 - SD - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.001
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95280",
      "displayName": "Media Services - Media Flow - Standard - H264 - SD - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.002
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95281",
      "displayName": "Media Services - Media Flow - Standard - H264 - SD - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.003
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95282",
      "displayName": "Media Services - Media Flow - Standard - H264 - HD - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.003
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95283",
      "displayName": "Media Services - Media Flow - Standard - H264 - HD - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.004
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95284",
      "displayName": "Media Services - Media Flow - Standard - H264 - HD - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.01
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95285",
      "displayName": "Media Services - Media Flow - Standard - H264 - 4k - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.015
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95286",
      "displayName": "Media Services - Media Flow - Standard - H264 - 4k - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.018
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95287",
      "displayName": "Media Services - Media Flow - Standard - H264 - 4k - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.036
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95288",
      "displayName": "Media Services - Media Flow - Standard - VP8 - SD - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.003
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95289",
      "displayName": "Media Services - Media Flow - Standard - VP8 - SD - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.004
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95290",
      "displayName": "Media Services - Media Flow - Standard - VP8 - SD - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.007
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95291",
      "displayName": "Media Services - Media Flow - Standard - VP8 - HD - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.008
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95292",
      "displayName": "Media Services - Media Flow - Standard - VP8 - HD - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.01
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95293",
      "displayName": "Media Services - Media Flow - Standard - VP8 - HD - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.015
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95294",
      "displayName": "Media Services - Media Flow - Standard - VP8 - 4k - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.036
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95295",
      "displayName": "Media Services - Media Flow - Standard - VP8 - 4k - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.04
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95296",
      "displayName": "Media Services - Media Flow - Standard - VP8 - 4k - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.05
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95297",
      "displayName": "Media Services - Media Flow - Standard - H265VP9 - SD - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.005
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95298",
      "displayName": "Media Services - Media Flow - Standard - H265VP9 - SD - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.007
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95299",
      "displayName": "Media Services - Media Flow - Standard - H265VP9 - SD - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.012
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95300",
      "displayName": "Media Services - Media Flow - Standard - H265VP9 - HD - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.01
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95301",
      "displayName": "Media Services - Media Flow - Standard - H265VP9 - HD - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.02
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95302",
      "displayName": "Media Services - Media Flow - Standard - H265VP9 - HD - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.03
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95303",
      "displayName": "Media Services - Media Flow - Standard - H265VP9 - 4k - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.026
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95304",
      "displayName": "Media Services - Media Flow - Standard - H265VP9 - 4k - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.05
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95305",
      "displayName": "Media Services - Media Flow - Standard - H265VP9 - 4k - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.063
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95306",
      "displayName": "Media Services - Media Flow - Speed - H264 - SD - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.002
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95307",
      "displayName": "Media Services - Media Flow - Speed - H264 - SD - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.003
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95308",
      "displayName": "Media Services - Media Flow - Speed - H264 - SD - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.004
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95309",
      "displayName": "Media Services - Media Flow - Speed - H264 - HD - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.004
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95310",
      "displayName": "Media Services - Media Flow - Speed - H264 - HD - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.005
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95311",
      "displayName": "Media Services - Media Flow - Speed - H264 - HD - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.012
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95312",
      "displayName": "Media Services - Media Flow - Speed - H264 - 4k - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.018
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95313",
      "displayName": "Media Services - Media Flow - Speed - H264 - 4k - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.02
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95314",
      "displayName": "Media Services - Media Flow - Speed - H264 - 4k - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.04
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95315",
      "displayName": "Media Services - Media Flow - Speed - VP8 - SD - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.005
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95316",
      "displayName": "Media Services - Media Flow - Speed - VP8 - SD - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.006
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95317",
      "displayName": "Media Services - Media Flow - Speed - VP8 - SD - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.008
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95318",
      "displayName": "Media Services - Media Flow - Speed - VP8 - HD - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.012
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95319",
      "displayName": "Media Services - Media Flow - Speed - VP8 - HD - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.015
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95320",
      "displayName": "Media Services - Media Flow - Speed - VP8 - HD - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.018
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95321",
      "displayName": "Media Services - Media Flow - Speed - VP8 - 4k - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.048
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95322",
      "displayName": "Media Services - Media Flow - Speed - VP8 - 4k - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.05
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95323",
      "displayName": "Media Services - Media Flow - Speed - VP8 - 4k - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.06
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95324",
      "displayName": "Media Services - Media Flow - Speed - H265VP9 - SD - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.006
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95325",
      "displayName": "Media Services - Media Flow - Speed - H265VP9 - SD - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.008
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95326",
      "displayName": "Media Services - Media Flow - Speed - H265VP9 - SD - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.015
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95327",
      "displayName": "Media Services - Media Flow - Speed - H265VP9 - HD - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.012
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95328",
      "displayName": "Media Services - Media Flow - Speed - H265VP9 - HD - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.025
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95329",
      "displayName": "Media Services - Media Flow - Speed - H265VP9 - HD - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.036
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95330",
      "displayName": "Media Services - Media Flow - Speed - H265VP9 - 4k - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.05
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95331",
      "displayName": "Media Services - Media Flow - Speed - H265VP9 - 4k - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.06
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95332",
      "displayName": "Media Services - Media Flow - Speed - H265VP9 - 4k - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.075
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95333",
      "displayName": "Media Services - Media Flow - Quality - H264 - SD - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.003
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95334",
      "displayName": "Media Services - Media Flow - Quality - H264 - SD - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.004
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95335",
      "displayName": "Media Services - Media Flow - Quality - H264 - SD - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.005
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95336",
      "displayName": "Media Services - Media Flow - Quality - H264 - HD - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.005
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95337",
      "displayName": "Media Services - Media Flow - Quality - H264 - HD - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.006
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95338",
      "displayName": "Media Services - Media Flow - Quality - H264 - HD - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.015
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95339",
      "displayName": "Media Services - Media Flow - Quality - H264 - 4k - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.024
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95340",
      "displayName": "Media Services - Media Flow - Quality - H264 - 4k - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.03
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95341",
      "displayName": "Media Services - Media Flow - Quality - H264 - 4k - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.05
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95342",
      "displayName": "Media Services - Media Flow - Quality - VP8 - SD - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.006
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95343",
      "displayName": "Media Services - Media Flow - Quality - VP8 - SD - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.008
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95344",
      "displayName": "Media Services - Media Flow - Quality - VP8 - SD - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.01
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95345",
      "displayName": "Media Services - Media Flow - Quality - VP8 - HD - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.015
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95346",
      "displayName": "Media Services - Media Flow - Quality - VP8 - HD - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.018
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95347",
      "displayName": "Media Services - Media Flow - Quality - VP8 - HD - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.02
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95348",
      "displayName": "Media Services - Media Flow - Quality - VP8 - 4k - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.054
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95349",
      "displayName": "Media Services - Media Flow - Quality - VP8 - 4k - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.06
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95350",
      "displayName": "Media Services - Media Flow - Quality - VP8 - 4k - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.07
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95351",
      "displayName": "Media Services - Media Flow - Quality - H265VP9 - SD - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.03
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95352",
      "displayName": "Media Services - Media Flow - Quality - H265VP9 - SD - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.045
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95353",
      "displayName": "Media Services - Media Flow - Quality - H265VP9 - SD - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.06
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95354",
      "displayName": "Media Services - Media Flow - Quality - H265VP9 - HD - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.06
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95355",
      "displayName": "Media Services - Media Flow - Quality - H265VP9 - HD - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.09
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95356",
      "displayName": "Media Services - Media Flow - Quality - H265VP9 - HD - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.12
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95357",
      "displayName": "Media Services - Media Flow - Quality - H265VP9 - 4k - Below 30fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.12
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95358",
      "displayName": "Media Services - Media Flow - Quality - H265VP9 - 4k - Above 30fps and Below 60fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.18
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95359",
      "displayName": "Media Services - Media Flow - Quality - H265VP9 - 4k - Above 60fps and Below 120fps",
      "metricName": "Minute of Output Media Content",
      "serviceCategory": "Media Services - Media Flow",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.24
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95375",
      "displayName": "Media Services - Media Streams",
      "metricName": "GB of packaged content",
      "serviceCategory": "Media Services - Media Streams",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.05
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95403",
      "displayName": "Network Firewall Instance",
      "description": "Enter the number of Network Firewall Instances",
      "metricName": "Instance Per Hour",
      "serviceCategory": "Network Firewall",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 2.75
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95404",
      "displayName": "Network Firewall Data Processing",
      "description": "Enter the estimated cumulative data processed for all Network Firewall Instances.  Each month, the first 10TB is free.",
      "metricName": "Gigabyte (GB) of Data Processed",
      "serviceCategory": "Network Firewall",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 10240
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.01,
              "rangeMin": 10240,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95410",
      "displayName": "Oracle ZFS Storage - High Availability",
      "metricName": "Instance Per Hour",
      "serviceCategory": "ZFS Storage",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.85
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95411",
      "displayName": "Oracle Cloud VMware Solution - BM.DenseIO.E4.32 - Hourly Commit",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.3047
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95412",
      "displayName": "Oracle Cloud VMware Solution - BM.DenseIO.E4.32 - Monthly Commit",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.2539
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95413",
      "displayName": "Oracle Cloud VMware Solution - BM.DenseIO.E4.32 - 1 year Commit",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.2031
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95414",
      "displayName": "Oracle Cloud VMware Solution - BM.DenseIO.E4.32 - 3 year Commit",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.165
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95415",
      "displayName": "Oracle Cloud VMware Solution - BM.DenseIO.E4.128 - Hourly Commit",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.195
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95416",
      "displayName": "Oracle Cloud VMware Solution - BM.DenseIO.E4.128 - Monthly Commit",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1625
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95417",
      "displayName": "Oracle Cloud VMware Solution - BM.DenseIO.E4.128 - 1 year Commit",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.13
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95418",
      "displayName": "Oracle Cloud VMware Solution - BM.DenseIO.E4.128 - 3 year Commit",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1056
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95427",
      "displayName": "MySQL Database - AWS - Storage",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "Database - MySQL HeatWave on AWS",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.132
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95428",
      "displayName": "MySQL Database - AWS - Backup Storage",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "Database - MySQL HeatWave on AWS",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0476
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95485",
      "displayName": "OCI Full Stack Disaster Recovery Service",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Full Stack Disaster Recovery",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0128
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95518",
      "displayName": "Secure Desktop",
      "metricName": "Desktop Per Month",
      "serviceCategory": "Secure Desktops",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 20
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95634",
      "displayName": "Logging Analytics - Active Storage",
      "description": "First 10 gigabytes of log storage are free",
      "metricName": "Logging Analytics Storage Unit Per Month",
      "serviceCategory": "Observability - Logging Analytics",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 372,
              "rangeMin": 0,
              "rangeMax": 35
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 260.4,
              "rangeMin": 35,
              "rangeMax": 103
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 223.2,
              "rangeMin": 103,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95697",
      "displayName": "OCI Queue",
      "metricName": "1,000,000 Requests",
      "serviceCategory": "OCI Queue",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 1
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.22,
              "rangeMin": 1,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95701",
      "displayName": "Oracle Autonomous Data Warehouse - ECPU",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Database - Autonomous Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.336
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95702",
      "displayName": "Oracle Autonomous Transaction Processing - ECPU",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Database - Autonomous Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.336
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95703",
      "displayName": "Oracle Autonomous Data Warehouse - ECPU - BYOL",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Database - Autonomous Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0807
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95704",
      "displayName": "Oracle Autonomous Transaction Processing - ECPU - BYOL",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Database - Autonomous Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0807
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95706",
      "displayName": "Oracle Autonomous Database Storage for Transaction Processing",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "Database - Autonomous Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1156
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95708",
      "displayName": "Oracle Autonomous Data Warehouse - Exadata Cloud@Customer - ECPU",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Database - Exadata Database - Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.336
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95709",
      "displayName": "Oracle Autonomous Transaction Processing - Exadata Cloud@Customer - ECPU - ECPU Per Hour",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Database - Exadata Database - Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.336
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95710",
      "displayName": "Oracle Autonomous Data Warehouse - Exadata Cloud@Customer - ECPU - BYOL - ECPU Per Hour",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Database - Exadata Database - Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0807
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95711",
      "displayName": "Oracle Autonomous Transaction Processing - Exadata Cloud@Customer - ECPU - BYOL - ECPU Per Hour",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Database - Exadata Database - Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0807
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95712",
      "displayName": "Oracle Autonomous Data Warehouse - Dedicated - ECPU",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Database - Autonomous Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.336
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95713",
      "displayName": "Oracle Autonomous Transaction Processing - Dedicated - ECPU",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Database - Autonomous Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.336
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95714",
      "displayName": "Oracle Autonomous Data Warehouse - Dedicated - ECPU - BYOL",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Database - Autonomous Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0807
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95715",
      "displayName": "Oracle Autonomous Transaction Processing - Dedicated - ECPU - BYOL",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Database - Autonomous Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0807
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95754",
      "displayName": "Oracle Autonomous Database Storage",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "Database - Autonomous Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0244
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95907",
      "displayName": "Compute - GPU - A100 - v2",
      "metricName": "GPU Per Hour",
      "serviceCategory": "Compute - GPU",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 4
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95909",
      "displayName": "Compute  - GPU - A10",
      "metricName": "GPU Per Hour",
      "serviceCategory": "Compute - GPU",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 2
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95917",
      "displayName": "OCI - Language - Custom Inferencing",
      "metricName": "1,000 Transactions",
      "serviceCategory": "OCI Language",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 3.5
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95918",
      "displayName": "OCI - Language - Custom Inferencing - Dedicated",
      "metricName": "Inferencing Unit Hour",
      "serviceCategory": "OCI Language",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 15
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.5,
              "rangeMin": 15,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95919",
      "displayName": "OCI - Language - Custom Training",
      "metricName": "Training Hour",
      "serviceCategory": "OCI Language",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 15
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.5,
              "rangeMin": 15,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B95920",
      "displayName": "OCI - Language - Text Translation",
      "metricName": "1,000 Transactions",
      "serviceCategory": "OCI Language",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 1
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 10,
              "rangeMin": 1,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96109",
      "displayName": "OCI Kubernetes Engine - Virtual Node",
      "metricName": "Virtual Node Per Hour",
      "serviceCategory": "Cloud Infrastructure Kubernetes Engine (OKE)",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.015
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96110",
      "displayName": "OCI - Document Understanding - OCR",
      "metricName": "1,000 Transactions",
      "serviceCategory": "OCI Document Understanding",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 5
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1,
              "rangeMin": 5,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96111",
      "displayName": "OCI - Document Understanding - Document Properties",
      "metricName": "1,000 Transactions",
      "serviceCategory": "OCI Document Understanding",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 5
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.25,
              "rangeMin": 5,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96112",
      "displayName": "OCI - Document Understanding - Document Extraction",
      "metricName": "1,000 Transactions",
      "serviceCategory": "OCI Document Understanding",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 5
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 10,
              "rangeMin": 5,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96113",
      "displayName": "OCI - Document Understanding - Custom Training",
      "metricName": "Training Hour",
      "serviceCategory": "OCI Document Understanding",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 15
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.5,
              "rangeMin": 15,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96157",
      "displayName": "MySQL HeatWave - AWS",
      "metricName": "HeatWave Capacity Per Hour",
      "serviceCategory": "HeatWave Cluster",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.049
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96158",
      "displayName": "MySQL Database - AWS - ECPU",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Database - MySQL HeatWave on AWS",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.049
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96159",
      "displayName": "MySQL Database - AWS - Outbound Data Transfer - Inter AWS Region",
      "metricName": "Gigabyte of Data Transferred",
      "serviceCategory": "Database - MySQL HeatWave on AWS",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0504
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96160",
      "displayName": "MySQL Database - AWS - Outbound Data Transfer - To Internet",
      "metricName": "Gigabyte of Data Transferred",
      "serviceCategory": "Database - MySQL HeatWave on AWS",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0911
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96199",
      "displayName": "OCI Ops Insights for Oracle Autonomous Databases - Basic",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Observability - Ops Insights",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96200",
      "displayName": "OCI Database Management for Oracle Cloud Databases",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Oracle Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.025
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96479",
      "displayName": "Oracle Compute Cloud@Customer - Compute - Standard - E5",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.03
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96480",
      "displayName": "Oracle Compute Cloud@Customer - Compute - Standard - E5 - Memory",
      "metricName": "Gibibyte Memory Per Hour",
      "serviceCategory": "Compute Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.002
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96481",
      "displayName": "Oracle Compute Cloud@Customer - Block Volume Storage - Balanced",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "Compute Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0425
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96482",
      "displayName": "Oracle Compute Cloud@Customer - Block Volume Storage - Performance",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "Compute Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0595
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96483",
      "displayName": "Oracle Compute Cloud@Customer - File Storage",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "Compute Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.3
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96484",
      "displayName": "Oracle Compute Cloud@Customer - Object Storage - Storage",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "Compute Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0255
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96485",
      "displayName": "Oracle Compute Cloud@Customer - Load Balancer",
      "metricName": "Load Balancer Hour",
      "serviceCategory": "Compute Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0113
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96531",
      "displayName": "OCI - Compute - HPC - E5",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Bare Metal",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.044
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96545",
      "displayName": "OCI Kubernetes Engine - Enhanced Cluster",
      "metricName": "Cluster Per Hour",
      "serviceCategory": "Cloud Infrastructure Kubernetes Engine (OKE)",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96582",
      "displayName": "Oracle Tuxedo for OCI",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Application Development - Tuxedo",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.5433
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96583",
      "displayName": "Oracle Tuxedo Enterprise Edition for OCI",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Application Development - Tuxedo",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.8149
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96584",
      "displayName": "Oracle Tuxedo Mainframe Modernization Runtimes for OCI",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Application Development - Tuxedo",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.0866
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96625",
      "displayName": "OCI - HeatWave - Storage",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "HeatWave Cluster",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.02
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96626",
      "displayName": "OCI - HeatWave",
      "metricName": "HeatWave Capacity Per Hour",
      "serviceCategory": "HeatWave Cluster",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.011
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B96629",
      "displayName": "OCI - Application Performance Monitoring Service - Synthetic Usage - Free",
      "description": "Always Free, limited to 10 Monitor Runs Per Hour.",
      "metricName": "10 Monitor Runs Per Hour",
      "serviceCategory": "Observability - APM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97102",
      "displayName": "Oracle Cloud VMware Solution - Base - BM.Standard2.12 - Hourly Commit",
      "metricName": "Node Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 4.714
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97103",
      "displayName": "Oracle Cloud VMware Solution - Base - BM.Standard2.12 - 1 Year Commit",
      "metricName": "Node Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 3.7144
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97104",
      "displayName": "Oracle Cloud VMware Solution - Base - BM.Standard2.12 - 3 Year Commit",
      "metricName": "Node Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 2.953
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97105",
      "displayName": "Oracle Cloud VMware Solution - Base - BM.Standard3.16 - Hourly Commit",
      "metricName": "Node Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 5.554
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97106",
      "displayName": "Oracle Cloud VMware Solution - Base - BM.Standard3.16 - 1 Year Commit",
      "metricName": "Node Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 4.2943
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97107",
      "displayName": "Oracle Cloud VMware Solution - Base - BM.Standard3.16 - 3 Year Commit",
      "metricName": "Node Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 3.3522
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97108",
      "displayName": "Oracle Cloud VMware Solution - Base - BM.Standard.E4.32 - Hourly Commit",
      "metricName": "Node Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 9.2565
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97109",
      "displayName": "Oracle Cloud VMware Solution - Base - BM.Standard.E4.32 - 1 Year Commit",
      "metricName": "Node Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 6.9223
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97110",
      "displayName": "Oracle Cloud VMware Solution - Base - BM.Standard.E4.32 - 3 Year Commit",
      "metricName": "Node Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 5.2233
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97111",
      "displayName": "Oracle Cloud VMware Solution - Expansion - Hourly Commit",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1605
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97112",
      "displayName": "Oracle Cloud VMware Solution - Expansion - 1 Year Commit",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1092
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97113",
      "displayName": "Oracle Cloud VMware Solution - Expansion - 3 Year Commit",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - VMware",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0739
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97140",
      "displayName": "OCI Ops Insights for Oracle Cloud Databases",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Observability - Ops Insights",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0075
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97172",
      "displayName": "Oracle Access Governance for OCI - Workforce User",
      "metricName": "Workforce User Per Month",
      "serviceCategory": "Access Governance",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1,
              "rangeMin": 0,
              "rangeMax": 100000
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.005,
              "rangeMin": 100000,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97173",
      "displayName": "Oracle Access Governance for Oracle Workloads - Workforce User",
      "metricName": "Workforce User Per Month",
      "serviceCategory": "Access Governance",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 2,
              "rangeMin": 0,
              "rangeMax": 10000
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.75,
              "rangeMin": 10000,
              "rangeMax": 30000
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.1,
              "rangeMin": 30000,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97179",
      "displayName": "Oracle Access Governance Premium - Consumer User",
      "metricName": "Consumer User Per Month",
      "serviceCategory": "Access Governance",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.016
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97180",
      "displayName": "Oracle Access Governance for Oracle Workloads - Consumer User",
      "metricName": "Consumer User Per Month",
      "serviceCategory": "Access Governance",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.016
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97181",
      "displayName": "Oracle Access Governance Premium - Workforce User",
      "metricName": "Workforce User Per Month",
      "serviceCategory": "Access Governance",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 3,
              "rangeMin": 0,
              "rangeMax": 10000
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.13,
              "rangeMin": 10000,
              "rangeMax": 30000
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.15,
              "rangeMin": 30000,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97191",
      "displayName": "Oracle NoSQL Database Cloud - Regional Replicated Write",
      "description": "Oracle NoSQL Database Cloud Service makes it easy for developers to build applications using document, fixed schema, and key-value database models, delivering predictable single-digit millisecond response times with data replication for high availability. The service offers active-active regional replication, ACID transactions, serverless scaling, comprehensive security, and low pay-per-use pricing for both on-demand and provisioned capacity modes, including 100% compatibility with on-premises Oracle NoSQL Database.",
      "metricName": "Write Unit Per Month",
      "serviceCategory": "Database - NoSQL",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.36
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97193",
      "displayName": "OCI - Document Understanding - Custom Document Properties",
      "metricName": "1,000 Transactions",
      "serviceCategory": "OCI Document Understanding",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 5
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.5,
              "rangeMin": 5,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97194",
      "displayName": "OCI - Document Understanding - Custom Document Extraction",
      "metricName": "1,000 Transactions",
      "serviceCategory": "OCI Document Understanding",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0,
              "rangeMin": 0,
              "rangeMax": 5
            },
            {
              "model": "PAY_AS_YOU_GO",
              "value": 30,
              "rangeMin": 5,
              "rangeMax": 999999999999999
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97197",
      "displayName": "Oracle Base Database Service on Arm - Enterprise",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Database - Base Database Service - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.2151
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97198",
      "displayName": "Oracle Base Database Service on Arm - High Performance",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Database - Base Database Service - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.4436
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97199",
      "displayName": "Oracle Base Database Service on Arm - Extreme Performance",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Database - Base Database Service - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.6721
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97200",
      "displayName": "Oracle Base Database Service on Arm - BYOL",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Database - Base Database Service - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0968
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97384",
      "displayName": "Compute - Standard - E5 - OCPU",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.03
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B97385",
      "displayName": "Compute - Standard - E5 - Memory",
      "metricName": "Gigabyte Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.002
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B98100",
      "displayName": "OCI - External Key Management",
      "metricName": "Key Version Per Month",
      "serviceCategory": "Security - Key Management",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 3
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B98202",
      "displayName": "Oracle Cloud Infrastructure - Compute - Dense I/O - E5 OCPU",
      "description": "Oracle Cloud Infrastructure (OCI) provides fast, flexible, and affordable compute capacity to fit any workload need, from high performance bare metal instances and flexible VMs to lightweight containers and serverless computing.",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.03
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B98203",
      "displayName": "Oracle Cloud Infrastructure - Compute - Dense I/O - E5 Memory",
      "description": "Oracle Cloud Infrastructure (OCI) provides fast, flexible, and affordable compute capacity to fit any workload need, from high performance bare metal instances and flexible VMs to lightweight containers and serverless computing.",
      "metricName": "Gigabyte Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.002
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B98204",
      "displayName": "Oracle Cloud Infrastructure - Compute - Dense I/O - E5 NVMe",
      "description": "Oracle Cloud Infrastructure (OCI) provides fast, flexible, and affordable compute capacity to fit any workload need, from high performance bare metal instances and flexible VMs to lightweight containers and serverless computing.",
      "metricName": "NVMe Terabyte Per Hour",
      "serviceCategory": "Compute - Virtual Machine",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0612
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B98217",
      "displayName": "Oracle Cloud Infrastructure Cache with Redis - Low Memory (up to 10 GB per node)",
      "description": "Oracle Cloud Infrastructure (OCI) Cache with Redis is a comprehensive, managed in-memory caching solution built on the foundation of open source Redis. This fully managed service accelerates data reads and writes, significantly enhancing application response times and database performance to provide an improved customer experience.",
      "metricName": "Redis Memory Gigabyte per Hour",
      "serviceCategory": "Cache with Redis",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0194
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B98277",
      "displayName": "Oracle Autonomous Transaction Processing - Exadata Cloud@Customer - Developer",
      "metricName": "Instance Per Hour",
      "serviceCategory": "Database - Exadata Database - Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B98278",
      "displayName": "Oracle Autonomous Data Warehouse - Exadata Cloud@Customer - Developer",
      "metricName": "Instance Per Hour",
      "serviceCategory": "Database - Exadata Database - Cloud@Customer",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B98279",
      "displayName": "Oracle Autonomous Transaction Processing - Dedicated - Developer",
      "metricName": "Instance Per Hour",
      "serviceCategory": "Database - Autonomous Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B98280",
      "displayName": "Oracle Autonomous Data Warehouse - Dedicated - Developer",
      "metricName": "Instance Per Hour",
      "serviceCategory": "Database - Autonomous Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B98415",
      "displayName": "Oracle Cloud Infrastructure - Compute - GPU - H100",
      "description": "Oracle Cloud Infrastructure (OCI) Compute provides bare metal and virtual machine instances powered by NVIDIA GPUs for a variety of use cases, including mainstream graphics and videos as well as the most demanding AI training and HPC workloads. OCI excels at both training time and cost because of its ultralow latency and near line-rate network performance.",
      "metricName": "GPU Per Hour",
      "serviceCategory": "Compute - GPU",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 10
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B99060",
      "displayName": "Database with PostgreSQL - X86",
      "metricName": "OCPU Per Hour",
      "serviceCategory": "Database with PostgreSQL",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.098
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B99062",
      "displayName": "Database Optimized Storage",
      "description": "Oracle Cloud Infrastructure Database with PostgreSQL is a fully managed service that uses the PostgreSQL database with a database-optimized storage layer. The service automates the complex and routine tasks associated with deploying and managing a distributed environment so you can focus on building great applications. We offer easy cluster creation, automated HA, patching, security updates, and automatic storage scaling.",
      "metricName": "Gigabyte Storage Capacity Per Month",
      "serviceCategory": "Database with PostgreSQL",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.072
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B99259",
      "displayName": "OCI - Application Performance Monitoring Service - Stack Monitoring - Enterprise Edition",
      "metricName": "10 Monitored Resources Per Hour",
      "serviceCategory": "Observability - APM",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.38
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B99591",
      "displayName": "Oracle Cloud Infrastructure Cache with Redis - High Memory (over 10 GB per node)",
      "description": "Oracle Cloud Infrastructure (OCI) Cache with Redis is a comprehensive, managed in-memory caching solution built on the foundation of open source Redis. This fully managed service accelerates data reads and writes, significantly enhancing application response times and database performance to provide an improved customer experience.",
      "metricName": "Redis Memory Gigabyte per Hour",
      "serviceCategory": "Cache with Redis",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0136
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B99593",
      "displayName": "Oracle Globally Distributed Autonomous Transaction Processing - Dedicated",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Database - Autonomous Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.3864
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B99594",
      "displayName": "Oracle Globally Distributed Autonomous Transaction Processing - Dedicated - BYOL",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Database - Autonomous Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0928
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B99595",
      "displayName": "Oracle Globally Distributed Autonomous Data Warehouse - Dedicated",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Database - Autonomous Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.3864
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B99596",
      "displayName": "Oracle Globally Distributed Autonomous Data Warehouse - Dedicated - BYOL",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Database - Autonomous Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0928
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B99597",
      "displayName": "OCI - Dedicated Key Management (Minimum 3 HSM Partitions)",
      "metricName": "HSM Partition Per Hour",
      "serviceCategory": "Security - Key Management",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 1.75
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B99708",
      "displayName": "Oracle Autonomous JSON Database - ECPU",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "Database - Autonomous JSON Database",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0807
            }
          ]
        }
      ]
    },
    {
      "partNumber": "B99709",
      "displayName": "Oracle APEX Application Development - ECPU",
      "metricName": "ECPU Per Hour",
      "serviceCategory": "APEX Application Development",
      "currencyCodeLocalizations": [
        {
          "currencyCode": "USD",
          "prices": [
            {
              "model": "PAY_AS_YOU_GO",
              "value": 0.0807
            }
          ]
        }
      ]
    }
  ]
}
`
