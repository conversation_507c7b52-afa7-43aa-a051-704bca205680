apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "karpenter.fullname" . }}-admin
  labels:
    rbac.authorization.k8s.io/aggregate-to-admin: "true"
    {{- include "karpenter.labels" . | nindent 4 }}
  {{- with .Values.additionalAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
rules:
  - apiGroups: ["karpenter.sh"]
    resources: ["nodepools", "nodepools/status", "nodeclaims", "nodeclaims/status"]
    verbs: ["get", "list", "watch", "create", "delete", "patch"]
  - apiGroups: ["karpenter.k8s.oracle"]
    resources: ["ocinodeclasses"]
    verbs: ["get", "list", "watch", "create", "delete", "patch"]
