apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "karpenter.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "karpenter.labels" . | nindent 4 }}
  {{- with .Values.additionalAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
rules:
  # Read
  - apiGroups: ["coordination.k8s.io"]
    resources: ["leases"]
    verbs: ["get", "watch"]
{{- if .Values.webhook.enabled }}
  - apiGroups: [""]
    resources: ["configmaps", "secrets"]
    verbs: ["get", "list", "watch"]
{{- end }}
  # Write
{{- if .Values.webhook.enabled }}
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["update"]
    resourceNames:
      - "{{ include "karpenter.fullname" . }}-cert"
{{- end }}
  - apiGroups: ["coordination.k8s.io"]
    resources: ["leases"]
    verbs: ["patch", "update"]
    resourceNames:
      - "karpenter-leader-election"
  # Cannot specify resourceNames on create
  # https://kubernetes.io/docs/reference/access-authn-authz/rbac/#referring-to-resources
  - apiGroups: ["coordination.k8s.io"]
    resources: ["leases"]
    verbs: ["create"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "karpenter.fullname" . }}-dns
  namespace: kube-system
  labels:
    {{- include "karpenter.labels" . | nindent 4 }}
  {{- with .Values.additionalAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
rules:
  # Read
  - apiGroups: [""]
    resources: ["services"]
    resourceNames: ["kube-dns"]
    verbs: ["get"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ include "karpenter.fullname" . }}-lease
  namespace: kube-node-lease
  labels:
    {{- include "karpenter.labels" . | nindent 4 }}
  {{- with .Values.additionalAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
rules:
  # Read
  - apiGroups: ["coordination.k8s.io"]
    resources: ["leases"]
    verbs: ["get", "list", "watch"]
  # Write
  - apiGroups: ["coordination.k8s.io"]
    resources: ["leases"]
    verbs: ["delete"]
