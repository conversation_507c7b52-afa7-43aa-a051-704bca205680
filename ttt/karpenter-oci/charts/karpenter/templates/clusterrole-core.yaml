apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: {{ include "karpenter.fullname" . }}-core
  labels:
    {{- include "karpenter.labels" . | nindent 4 }}
  {{- with .Values.additionalAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {{ include "karpenter.fullname" . }}-core
subjects:
  - kind: ServiceAccount
    name: {{ template "karpenter.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: {{ include "karpenter.fullname" . }}-core
  labels:
    {{- include "karpenter.labels" . | nindent 4 }}
  {{- with .Values.additionalAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
rules:
  # Read
  - apiGroups: ["karpenter.sh"]
    resources: ["nodepools", "nodepools/status", "nodeclaims", "nodeclaims/status"]
    verbs: ["get", "list", "watch"]
  - apiGroups: [""]
    resources: ["pods", "nodes", "persistentvolumes", "persistentvolumeclaims", "replicationcontrollers", "namespaces"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["storage.k8s.io"]
    resources: ["storageclasses", "csinodes", "volumeattachments"]
    verbs: ["get", "watch", "list"]
  - apiGroups: ["apps"]
    resources: ["daemonsets", "deployments", "replicasets", "statefulsets"]
    verbs: ["list", "watch"]
  {{- if .Values.webhook.enabled }}
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    verbs: ["get", "watch", "list"]
  {{- end }}
  - apiGroups: ["policy"]
    resources: ["poddisruptionbudgets"]
    verbs: ["get", "list", "watch"]
  # Write
  - apiGroups: ["karpenter.sh"]
    resources: ["nodeclaims", "nodeclaims/status"]
    verbs: ["create", "delete", "update", "patch"]
  - apiGroups: ["karpenter.sh"]
    resources: ["nodepools", "nodepools/status"]
    verbs: ["update", "patch"]
  - apiGroups: [""]
    resources: ["events"]
    verbs: ["create", "patch"]
  - apiGroups: [""]
    resources: ["nodes"]
    verbs: ["patch", "delete", "update"]
  - apiGroups: [""]
    resources: ["pods/eviction"]
    verbs: ["create"]
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["delete"]
  {{- if .Values.webhook.enabled }}
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions/status"]
    resourceNames: ["ocinodeclasses.karpenter.k8s.oracle", "nodepools.karpenter.sh", "nodeclaims.karpenter.sh"]
    verbs: ["patch"]
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    resourceNames: ["ocinodeclasses.karpenter.k8s.oracle", "nodepools.karpenter.sh", "nodeclaims.karpenter.sh"]
    verbs: ["update"]
  {{- end }}
  {{- with .Values.additionalClusterRoleRules -}}
  {{ toYaml . | nindent 2 }}
  {{- end -}}
