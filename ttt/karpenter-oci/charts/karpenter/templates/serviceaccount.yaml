{{- if .Values.serviceAccount.create -}}
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {{ include "karpenter.serviceAccountName" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "karpenter.labels" . | nindent 4 }}
  {{- if or .Values.additionalAnnotations .Values.serviceAccount.annotations }}
  annotations:
  {{- with .Values.additionalAnnotations }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with .Values.serviceAccount.annotations }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- end }}
{{- end -}}
