{{- if.Values.serviceMonitor.enabled -}}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "karpenter.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "karpenter.labels" . | nindent 4 }}
  {{- with .Values.serviceMonitor.additionalLabels }}
    {{- toYaml . | nindent 4 }}
  {{- end }}
  {{- with .Values.additionalAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  namespaceSelector:
    matchNames:
      - {{ .Release.Namespace }}
  selector:
    matchLabels:
      {{- include "karpenter.selectorLabels" . | nindent 6 }}
  endpoints:
    - port: http-metrics
      path: /metrics
    {{- with .Values.serviceMonitor.endpointConfig }}
      {{- toYaml . | nindent 6 }}
    {{- end }}
{{- end -}}
