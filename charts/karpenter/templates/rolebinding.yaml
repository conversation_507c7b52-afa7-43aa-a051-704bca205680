apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "karpenter.fullname" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    {{- include "karpenter.labels" . | nindent 4 }}
  {{- with .Values.additionalAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "karpenter.fullname" . }}
subjects:
  - kind: ServiceAccount
    name: {{ template "karpenter.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "karpenter.fullname" . }}-dns
  namespace: kube-system
  labels:
    {{- include "karpenter.labels" . | nindent 4 }}
  {{- with .Values.additionalAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "karpenter.fullname" . }}-dns
subjects:
  - kind: ServiceAccount
    name: {{ template "karpenter.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
--- 
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "karpenter.fullname" . }}-lease
  namespace: kube-node-lease
  labels:
    {{- include "karpenter.labels" . | nindent 4 }}
  {{- with .Values.additionalAnnotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: {{ include "karpenter.fullname" . }}-lease
subjects:
  - kind: ServiceAccount
    name: {{ template "karpenter.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}