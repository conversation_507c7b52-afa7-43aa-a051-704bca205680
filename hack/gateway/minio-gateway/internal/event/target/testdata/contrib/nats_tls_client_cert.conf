port: 14226
net: localhost

tls {
    cert_file:  "./testdata/contrib/certs/nats_server_cert.pem"
    key_file:   "./testdata/contrib/certs/nats_server_key.pem"
    ca_file:   "./testdata/contrib/certs/root_ca_cert.pem"
    verify_and_map: true
}
authorization {
    ADMIN = {
        publish = ">"
        subscribe = ">"
    }
    users = [
        {user: "CN=localhost,OU=Client,O=MinIO,C=CA", permissions: $ADMIN}
    ]
}
