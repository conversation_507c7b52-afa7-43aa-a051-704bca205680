/*
 * MinIO Object Storage (c) 2021 MinIO, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package ocioss

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	"path/filepath"
	"sort"
	"strings"
	"time"

	minio "seawave-gateway/cmd"
	"seawave-gateway/internal/logger"

	"github.com/dustin/go-humanize"
	"github.com/minio/cli"
	"github.com/minio/madmin-go"
	"github.com/minio/pkg/bucket/policy"
	"github.com/minio/pkg/env"
	"github.com/oracle/oci-go-sdk/v65/common"
	"github.com/oracle/oci-go-sdk/v65/objectstorage"
	// logger "log"
)

const (
	ociDefaultUploadPartSize   = 25 * humanize.MiByte                                     // OCI默认分片上传大小
	ociUploadRetryAttempts     = 5                                                        // OCI上传重试次数
	ociS3MinPartSize           = 5 * humanize.MiByte                                      // OCI最小分片大小
	metadataObjectNameTemplate = minio.GatewayMinioSysTmp + "multipart/v1/%s.%x/oci.json" // 元数据对象名称模板
	ociMarkerPrefix            = "{minio}"                                                // OCI标记前缀
	metadataPartNamePrefix     = minio.GatewayMinioSysTmp + "multipart/v1/%s.%x"          // 分片元数据前缀
	maxPartsCount              = 10000                                                    // 最大分片数
	partMetaVersionV1          = "v1"                                                     // 分片元数据版本
)

var (
	ociUploadPartSize    int // OCI上传分片大小
	ociUploadConcurrency int // OCI上传并发数
)

func init() {
	const ociGatewayTemplate = `NAME:
  {{.HelpName}} - {{.Usage}}

USAGE:
  {{.HelpName}} {{if .VisibleFlags}}[FLAGS]{{end}} [ENDPOINT]
{{if .VisibleFlags}}
FLAGS:
  {{range .VisibleFlags}}{{.}}
  {{end}}{{end}}
ENDPOINT:
  OCI Object Storage server endpoint. Default ENDPOINT is https://<namespace>.objectstorage.<region>.oci.customer-oci.com

EXAMPLES:
  1. Start minio gateway server for OCI Object Storage backend.
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_ROOT_USER{{.AssignmentOperator}}ocikeyid
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_ROOT_PASSWORD{{.AssignmentOperator}}ocikeysecret
     {{.Prompt}} {{.HelpName}}

  2. Start minio gateway server for OCI Object Storage with edge caching enabled.
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_ROOT_USER{{.AssignmentOperator}}ocikeyid
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_ROOT_PASSWORD{{.AssignmentOperator}}ocikeysecret
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_CACHE_DRIVES{{.AssignmentOperator}}"/mnt/drive1,/mnt/drive2,/mnt/drive3,/mnt/drive4"
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_CACHE_EXCLUDE{{.AssignmentOperator}}"bucket1/*,*.png"
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_CACHE_QUOTA{{.AssignmentOperator}}90
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_CACHE_AFTER{{.AssignmentOperator}}3
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_CACHE_WATERMARK_LOW{{.AssignmentOperator}}75
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_CACHE_WATERMARK_HIGH{{.AssignmentOperator}}85
     {{.Prompt}} {{.HelpName}}
`

	minio.RegisterGatewayCommand(cli.Command{
		Name:               minio.OCIBackendGateway,
		Usage:              "Oracle Cloud Infrastructure Object Storage (OCI OSS)",
		Action:             ociGatewayMain,
		CustomHelpTemplate: ociGatewayTemplate,
		HideHelpCommand:    true,
	})
}

// 检查是否是OCI返回的标记
func isOCIMarker(marker string) bool {
	return strings.HasPrefix(marker, ociMarkerPrefix)
}

// OCI网关主入口函数
func ociGatewayMain(ctx *cli.Context) {
	// 验证网关参数
	host := env.Get("OCI_HOST", "https://objectstorage.us-phoenix-1.oraclecloud.com")
	serverAddr := ctx.GlobalString("address")
	if serverAddr == "" || serverAddr == ":"+minio.GlobalMinioDefaultPort {
		serverAddr = ctx.String("address")
	}

	// 验证网关参数
	logger.FatalIf(minio.ValidateGatewayArguments(serverAddr, host), "Invalid argument")

	minio.StartGateway(ctx, &OCI{host})
}

// OCI网关实现结构体
type OCI struct {
	host string
}

// 返回网关名称
func (g *OCI) Name() string {
	return minio.OCIBackendGateway
}

// 初始化OCI客户端并返回OCI对象层
func (g *OCI) NewGatewayLayer(creds madmin.Credentials) (minio.ObjectLayer, error) {
	var err error

	// 初始化OCI配置提供者
	// configProvider := common.NewRawConfigurationProvider(
	// 	env.Get("OCI_TENANCY_OCID", "ocid1.tenancy.oc1..aaaaaaaacfchxfdz6qhnnmlmxep7qpzj36q4ceojog4pvkct7woxillugaca"),
	// 	env.Get("USER_ID", "ocid1.user.oc1..aaaaaaaaacw7zr2amqkyq5dulkje5aernndtga2s3epp55nidqy7vwykktvq"),
	// 	env.Get("OCI_REGION", "us-phoenix-1"),
	// 	env.Get("OCI_FINGERPRINT", "c6:90:c8:64:2e:b2:ee:c5:b9:e3:1e:b1:54:4a:0c:89"),
	// 	env.Get("PRIVATEKEY", "-----BEGIN PRIVATE KEY-----\nMIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC+QJNsljSX9LqV\nJoFTcSg3Mqu4czxISF+yuOsW0gqrsRaNrNigq++W0G4QKSaChq5IhEdtFkgcn+Pm\nv4DJ7AD9XzfM8dhtlEBW+9PgK931PE1VWq1rc7CdHKNf6is0Sd961qAh9dLLdiP+\noqqLPo5C3SWDiKYQan/rZQldkHtvq4zZvqAHykCSF5hw0BjHCRVO9Nyw/KpuN+79\n2A9lRX19jfGggM1gF3Nq3HzFCdXfjsGRnMna0ejdjbdLQxAHs3OqLAB20MenoR5R\nCmgO8StTffWgIOQ0WGzdk/tGRyH8WMO75Do3oyfrYrXQVhthkiETqvgFOiah7UyT\n/ldv5KZ5AgMBAAECggEAHImmM38Sc4q3pCDK/F+4ZxM4M+pBLByDElVvmhQ2mott\nNSIsumkf/rDUGaN6jdCy+tiwuBnEBlsrfdTS9jbq7Ran0ZujWciHoCSVIwahfCQn\n+3onEjufzelaTkxbX7QHDC9+hKmRjYhynML8rvdasofmlSTduA+pkX8t2PWPtm+0\nU+mqXQR4YgNRBnMDBDIO/fRy64jXDLNC3DXgUsjiGuZfHzcqGkaZ+PzKTpkeCx9I\nBvnhdKkwoSy+/09/D1bAXahn2C/Uk6/+kG6d3kkB5nhJ4pAGunVDDIukCpstBNw9\nGAt9ctExbKgWzHqnpUVBkswJ/PiNhchfoZ0fQ1IXswKBgQDqhyZ3AJufc46/twZU\nxCczNiumaphu3+0MQxgmECJ3+z6EuOxNg4WDPD2TRuk+1IQX6SZBikSx02l5hQeO\nFopfSxQ5SP0rv2b+3IhN+xlctAOL7cSG5JraPmeBcIEC8alFJ/CC94PyZ9CTVV5n\nI4MFpQ5URAFAhL+wfs2dzA9thwKBgQDPq7H5HsECvGRwkPRKy9DcOWDatr2KGHx0\nMo9bO8RgfYTtSVUAkqIae+r1VNczn7QcOHLFch19ZNwq0JIwlHiUIgggiaTe7+71\n+Rs3rRY8jq0yiCm7gO7Xx5xwTCcH9IErAPI0ob9YJyZJDxRLVimzEdzMWmI6DLkC\n9axzeRBL/wKBgGIGGw2iM2lx4zg+R2cmlTvSaGwmIZA2B94YLP+7vqxnVroikKHV\nVviDNTlZaiPYr11PFa5PHGc29d7Uu1OoGC28M7e9yhN+7I2Zn174lKEqnFDgEERR\nSGfvrVNssn+IgjdleiFDUuL+gS8Tf2Gcdibl1hdiVSOiWHxsg4RwxgIBAoGBAJ38\nD1PmyozLR1m/XKl+LnErEXyx4rWo9RzszdRpPnbtZm+iBxF9e+bL+K4UkfbK+xkw\n9Fcy/i5DyZ6kt2JPwP9oeGhQbmXPgCoODV+oxK7jF+3GWoEzkAAF8gQYWuUZZB6U\nBK53knp8pgqUdyrs7KI255Nq+ykQPrt6rGvYYMZ/AoGBAJvtaJSz/3z3blHVrHeF\nnvsAkm8o+vCI5qdfVcyOrevzmuYDS98LNR49iCOfPjM1zhGsWXvHy642tBddx9Z5\n+Z7/78R+5d9dFxgO25pD8o6JMsSxdEhTfwFSqAfDOQZaQtFOjK9wQoHjFYJyWAMa\n4vgX45zQZ9IPk32sFKUvPuQJ\n-----END PRIVATE KEY-----\nOCI_API_KEY"),
	// 	nil,
	// )

	configProvider := common.DefaultConfigProvider()

	// 创建自定义的HTTP客户端配置
	customTransport := &http.Transport{
		MaxIdleConns:           1000,             // 增加最大空闲连接数
		MaxIdleConnsPerHost:    100,              // 每个主机的最大空闲连接数
		IdleConnTimeout:        90 * time.Second, // 空闲连接超时时间
		DisableCompression:     true,             // 禁用压缩
		TLSHandshakeTimeout:    10 * time.Second, // TLS握手超时时间
		ResponseHeaderTimeout:  30 * time.Second, // 响应头超时时间
		ExpectContinueTimeout:  1 * time.Second,  // 100-continue超时时间
		MaxConnsPerHost:        100,              // 每个主机的最大连接数
		MaxResponseHeaderBytes: 1 << 20,          // 最大响应头大小
		ReadBufferSize:         32 * 1024,        // 读取缓冲区大小
		WriteBufferSize:        32 * 1024,        // 写入缓冲区大小
	}

	// 创建自定义的HTTP客户端
	customClient := &http.Client{
		Transport: customTransport,
		Timeout:   30 * time.Minute, // 设置整体超时时间
	}

	// 创建OCI对象存储客户端
	client, err := objectstorage.NewObjectStorageClientWithConfigurationProvider(configProvider)
	if err != nil {
		return nil, err
	}
	fmt.Print(13, &client)

	// 设置上传分片大小
	ociUploadPartSize, err = env.GetInt("MINIO_OCI_PART_SIZE_MB", 5)
	if err != nil {
		return nil, err
	}
	ociUploadPartSize *= humanize.MiByte
	if ociUploadPartSize <= 0 || ociUploadPartSize > 100*humanize.MiByte {
		return nil, fmt.Errorf("MINIO_OCI_PART_SIZE_MB should be an integer value between 0 and 100")
	}

	// 设置上传并发数
	ociUploadConcurrency, err = env.GetInt("MINIO_OCI_UPLOAD_CONCURRENCY", 4)
	if err != nil {
		return nil, err
	}

	return &ociObjects{
		Client:     client,
		httpClient: customClient,
		metrics:    minio.NewMetrics(),
		namespace:  env.Get("OCI_NAMESPACE", "sehubjapacprod"), // OCI命名空间
	}, nil
}

// OCI对象存储实现结构体
type ociObjects struct {
	minio.GatewayUnsupported
	Client     objectstorage.ObjectStorageClient // OCI客户端
	httpClient *http.Client                      // HTTP客户端
	metrics    *minio.BackendMetrics             // 指标
	namespace  string                            // OCI命名空间
}

// 将OCI错误转换为MinIO对象层错误
func ociToObjectError(err error, params ...string) error {
	if err == nil {
		return nil
	}

	bucket := ""
	object := ""
	if len(params) >= 1 {
		bucket = params[0]
	}
	if len(params) == 2 {
		object = params[1]
	}

	// 检查是否是OCI服务错误
	if serviceErr, ok := err.(common.ServiceError); ok {
		switch serviceErr.GetHTTPStatusCode() {
		case http.StatusNotFound:
			if object != "" {
				return minio.ObjectNotFound{Bucket: bucket, Object: object}
			}
			return minio.BucketNotFound{Bucket: bucket}
		case http.StatusBadRequest:
			if bucket != "" {
				return minio.BucketNameInvalid{Bucket: bucket}
			}
			return minio.ObjectNameInvalid{Bucket: bucket, Object: object}
		case http.StatusConflict:
			return minio.BucketAlreadyExists{Bucket: bucket}
		case http.StatusForbidden:
			return minio.PrefixAccessDenied{Bucket: bucket, Object: object}
		}
	}

	return err
}

// 获取指标
func (o *ociObjects) GetMetrics(ctx context.Context) (*minio.BackendMetrics, error) {
	return o.metrics, nil
}

// 关闭网关
func (o *ociObjects) Shutdown(ctx context.Context) error {
	return nil
}

// 存储信息
func (o *ociObjects) StorageInfo(ctx context.Context) (si minio.StorageInfo, _ []error) {
	si.Backend.Type = madmin.Gateway
	si.Backend.GatewayOnline = true
	return si, nil
}

// 创建存储桶
func (o *ociObjects) MakeBucketWithLocation(ctx context.Context, bucket string, opts minio.BucketOptions, storageAccountName string) error {
	// 创建OCI存储桶请求
	req := objectstorage.CreateBucketRequest{
		NamespaceName: common.String(o.namespace),
		CreateBucketDetails: objectstorage.CreateBucketDetails{
			CompartmentId: common.String(env.Get("OCI_COMPARTMENT_OCID", "")),
			Name:          common.String(bucket),
		},
	}

	// 发送创建请求
	_, err := o.Client.CreateBucket(ctx, req)
	if err != nil {
		return ociToObjectError(err, bucket)
	}

	return nil
}

// 获取存储桶信息
func (o *ociObjects) GetBucketInfo(ctx context.Context, bucket string, storageAccountName string) (bi minio.BucketInfo, e error) {
	// 获取OCI存储桶信息请求
	req := objectstorage.GetBucketRequest{
		NamespaceName: common.String(o.namespace),
		BucketName:    common.String(bucket),
	}

	// 发送请求
	resp, err := o.Client.GetBucket(ctx, req)
	if err != nil {
		return bi, ociToObjectError(err, bucket)
	}

	return minio.BucketInfo{
		Name:    bucket,
		Created: resp.TimeCreated.Time,
	}, nil
}

// 列出所有存储桶
func (o *ociObjects) ListBuckets(ctx context.Context, storageAccountName string) (buckets []minio.BucketInfo, err error) {
	// 列出OCI存储桶请求
	req := objectstorage.ListBucketsRequest{
		NamespaceName: common.String(o.namespace),
		CompartmentId: common.String(env.Get("OCI_COMPARTMENT_OCID", "")),
	}

	// 发送请求
	resp, err := o.Client.ListBuckets(ctx, req)
	if err != nil {
		return nil, ociToObjectError(err)
	}

	// 转换结果
	for _, bucket := range resp.Items {
		buckets = append(buckets, minio.BucketInfo{
			Name:    *bucket.Name,
			Created: bucket.TimeCreated.Time,
		})
	}

	return buckets, nil
}

// 删除存储桶
func (o *ociObjects) DeleteBucket(ctx context.Context, bucket string, opts minio.DeleteBucketOptions, storageAccountName string) error {
	// 删除OCI存储桶请求
	req := objectstorage.DeleteBucketRequest{
		NamespaceName: common.String(o.namespace),
		BucketName:    common.String(bucket),
	}

	// 发送请求
	_, err := o.Client.DeleteBucket(ctx, req)
	if err != nil {
		return ociToObjectError(err, bucket)
	}

	return nil
}

// ListObjects - 列出存储桶中的对象
func (o *ociObjects) ListObjects(ctx context.Context, bucket, prefix, marker, delimiter string, maxKeys int, storageAccountName string) (result minio.ListObjectsInfo, err error) {
	// 如果标记不为空，检查是否是MinIO格式
	if marker != "" {
		if strings.HasPrefix(marker, ociMarkerPrefix) {
			marker = strings.TrimPrefix(marker, ociMarkerPrefix)
		}
	}

	// 创建列出对象请求
	req := objectstorage.ListObjectsRequest{
		NamespaceName: common.String(o.namespace),
		BucketName:    common.String(bucket),
		Prefix:        common.String(prefix),
		Start:         common.String(marker), // 使用Start作为起始标记
		// Delimiter:     common.String(delimiter),
		Limit: common.Int(maxKeys),
	}

	// 发送请求
	resp, err := o.Client.ListObjects(ctx, req)
	if err != nil {
		return result, ociToObjectError(err, bucket)
	}

	// OCI使用NextStartWith作为分页标记
	result.IsTruncated = resp.NextStartWith != nil
	if resp.NextStartWith != nil {
		result.NextMarker = ociMarkerPrefix + *resp.NextStartWith
	}

	// 填充对象信息
	for _, obj := range resp.ListObjects.Objects {
		if obj.Name == nil || *obj.Name == "" {
			continue
		}
		result.Objects = append(result.Objects, minio.ObjectInfo{
			Bucket:      bucket,
			Name:        *obj.Name,
			ModTime:     obj.TimeModified.Time,
			Size:        *obj.Size,
			ETag:        strings.Trim(*obj.Etag, "\""),
			ContentType: getContentType(*obj.Name),
		})
	}

	// 填充前缀
	for _, pre := range resp.ListObjects.Prefixes {
		result.Prefixes = append(result.Prefixes, pre)
	}

	return result, nil
}

// ListObjectsV2 - 列出对象V2版本
func (o *ociObjects) ListObjectsV2(ctx context.Context, bucket, prefix, continuationToken, delimiter string, maxKeys int, fetchOwner bool, startAfter string, storageAccountName string) (result minio.ListObjectsV2Info, err error) {
	marker := continuationToken
	if marker == "" {
		marker = startAfter
	}

	// 调用V1版本实现
	resultV1, err := o.ListObjects(ctx, bucket, prefix, marker, delimiter, maxKeys, storageAccountName)
	if err != nil {
		return result, err
	}

	result.IsTruncated = resultV1.IsTruncated
	result.ContinuationToken = continuationToken
	result.NextContinuationToken = resultV1.NextMarker
	result.Objects = resultV1.Objects
	result.Prefixes = resultV1.Prefixes
	return result, nil
}

// GetObjectNInfo - 获取对象信息和读取器
func (o *ociObjects) GetObjectNInfo(ctx context.Context, bucket, object string, rs *minio.HTTPRangeSpec, h http.Header, lockType minio.LockType, opts minio.ObjectOptions, storageAccountName string) (gr *minio.GetObjectReader, err error) {
	var objInfo minio.ObjectInfo
	objInfo, err = o.GetObjectInfo(ctx, bucket, object, opts, storageAccountName)
	if err != nil {
		return nil, err
	}

	var startOffset, length int64
	startOffset, length, err = rs.GetOffsetLength(objInfo.Size)
	if err != nil {
		return nil, err
	}

	pr, pw := io.Pipe()
	go func() {
		err := o.getObject(ctx, bucket, object, startOffset, length, pw, objInfo.ETag, opts)
		pw.CloseWithError(err)
	}()

	// 设置清理函数
	pipeCloser := func() { pr.Close() }
	return minio.NewGetObjectReaderFromReader(pr, objInfo, opts, pipeCloser)
}

// getObject - 读取对象数据
func (o *ociObjects) getObject(ctx context.Context, bucket, object string, startOffset int64, length int64, writer io.Writer, etag string, opts minio.ObjectOptions) error {
	// 获取对象请求
	req := objectstorage.GetObjectRequest{
		NamespaceName: common.String(o.namespace),
		BucketName:    common.String(bucket),
		ObjectName:    common.String(object),
	}

	// 设置范围
	if startOffset >= 0 && length >= 0 {
		rangeHeader := fmt.Sprintf("bytes=%d-%d", startOffset, startOffset+length-1)
		req.Range = common.String(rangeHeader)
	}

	// 发送请求
	resp, err := o.Client.GetObject(ctx, req)
	if err != nil {
		return ociToObjectError(err, bucket, object)
	}
	defer resp.Content.Close()

	// 复制数据到写入器
	_, err = io.Copy(writer, resp.Content)
	return err
}

// GetObjectInfo - 获取对象信息
func (o *ociObjects) GetObjectInfo(ctx context.Context, bucket, object string, opts minio.ObjectOptions, storageAccountName string) (objInfo minio.ObjectInfo, err error) {
	logger.Info("开始获取对象HeadObject信息")
	// 获取对象元数据请求
	req := objectstorage.HeadObjectRequest{
		NamespaceName: common.String(o.namespace),
		BucketName:    common.String(bucket),
		ObjectName:    common.String(object),
	}

	// 发送请求
	resp, err := o.Client.HeadObject(ctx, req)
	if err != nil {
		logger.Error("获取对象HeadObject 错误")
		return objInfo, ociToObjectError(err, bucket, object)
	}

	// 处理可能为nil的指针字段
	var contentEncoding string
	if resp.ContentEncoding != nil {
		contentEncoding = *resp.ContentEncoding
	}

	var contentType string
	if resp.ContentType != nil {
		contentType = *resp.ContentType
	} else {
		contentType = getContentType(object)
	}

	var eTag string
	if resp.ETag != nil {
		eTag = strings.Trim(*resp.ETag, "\"")
	}

	var contentLength int64
	if resp.ContentLength != nil {
		contentLength = *resp.ContentLength
	}

	objInfo = minio.ObjectInfo{
		Bucket:          bucket,
		Name:            object,
		ModTime:         resp.LastModified.Time,
		Size:            contentLength,
		ETag:            eTag,
		ContentType:     contentType,
		ContentEncoding: contentEncoding,
	}
	logger.Info("获取对象HeadObject 成功")
	return objInfo, nil
}

// PutObject - 上传对象
func (o *ociObjects) PutObject(ctx context.Context, bucket, object string, r *minio.PutObjReader, opts minio.ObjectOptions, storageAccountName string) (objInfo minio.ObjectInfo, err error) {
	// logger.Info("接收到PUT请求准备开始上传")
	data := r.Reader

	// 获取内容类型
	contentType := opts.UserDefined["content-type"]
	if contentType == "" {
		contentType = getContentType(object)
	}

	// 创建上传对象请求
	req := objectstorage.PutObjectRequest{
		NamespaceName: common.String(o.namespace),
		BucketName:    common.String(bucket),
		ObjectName:    common.String(object),
		PutObjectBody: data,
		ContentType:   common.String(contentType),
		ContentLength: common.Int64(data.Size()),
	}

	// 发送请求
	_, err = o.Client.PutObject(ctx, req)
	if err != nil {
		logger.Error("接收到PUT请求上传错误")
		return objInfo, ociToObjectError(err, bucket, object)
	}
	// logger.Info("成功结束上传请求")
	// 获取对象信息返回
	return o.GetObjectInfo(ctx, bucket, object, opts, storageAccountName)
}

// CopyObject - 复制对象
func (o *ociObjects) CopyObject(ctx context.Context, srcBucket, srcObject, destBucket, destObject string, srcInfo minio.ObjectInfo, srcOpts, dstOpts minio.ObjectOptions, storageAccountName string) (objInfo minio.ObjectInfo, err error) {
	// 首先读取源对象
	getReq := objectstorage.GetObjectRequest{
		NamespaceName: common.String(o.namespace),
		BucketName:    common.String(srcBucket),
		ObjectName:    common.String(srcObject),
	}

	getResp, err := o.Client.GetObject(ctx, getReq)
	if err != nil {
		return objInfo, ociToObjectError(err, srcBucket, srcObject)
	}
	defer getResp.Content.Close()

	// 准备目标对象的内容类型
	contentType := dstOpts.UserDefined["content-type"]
	if contentType == "" {
		contentType = getContentType(destObject)
	}

	// 上传到目标位置
	putReq := objectstorage.PutObjectRequest{
		NamespaceName: common.String(o.namespace),
		BucketName:    common.String(destBucket),
		ObjectName:    common.String(destObject),
		PutObjectBody: getResp.Content,
		ContentType:   common.String(contentType),
		ContentLength: getResp.ContentLength,
	}

	_, err = o.Client.PutObject(ctx, putReq)
	if err != nil {
		return objInfo, ociToObjectError(err, destBucket, destObject)
	}

	// 获取目标对象信息返回
	return o.GetObjectInfo(ctx, destBucket, destObject, dstOpts, storageAccountName)
}

// DeleteObject - 删除对象
func (o *ociObjects) DeleteObject(ctx context.Context, bucket, object string, opts minio.ObjectOptions, storageAccountName string) (minio.ObjectInfo, error) {
	// 创建删除对象请求
	req := objectstorage.DeleteObjectRequest{
		NamespaceName: common.String(o.namespace),
		BucketName:    common.String(bucket),
		ObjectName:    common.String(object),
	}

	// 发送请求
	_, err := o.Client.DeleteObject(ctx, req)
	if err != nil {
		return minio.ObjectInfo{}, ociToObjectError(err, bucket, object)
	}

	return minio.ObjectInfo{
		Bucket: bucket,
		Name:   object,
	}, nil
}

// DeleteObjects - 批量删除对象
func (o *ociObjects) DeleteObjects(ctx context.Context, bucket string, objects []minio.ObjectToDelete, opts minio.ObjectOptions, storageAccountName string) ([]minio.DeletedObject, []error) {
	errs := make([]error, len(objects))
	dobjects := make([]minio.DeletedObject, len(objects))

	// 批量删除对象
	for i, obj := range objects {
		// 创建删除请求
		req := objectstorage.DeleteObjectRequest{
			NamespaceName: common.String(o.namespace),
			BucketName:    common.String(bucket),
			ObjectName:    common.String(obj.ObjectName),
		}

		// 发送请求
		_, err := o.Client.DeleteObject(ctx, req)
		if err != nil {
			errs[i] = ociToObjectError(err, bucket, obj.ObjectName)
			continue
		}

		dobjects[i] = minio.DeletedObject{
			ObjectName: obj.ObjectName,
		}
	}

	return dobjects, errs
}

// IsCompressionSupported - 是否支持压缩
func (o *ociObjects) IsCompressionSupported() bool {
	return false
}

// IsNotificationSupported - 是否支持通知
func (o *ociObjects) IsNotificationSupported() bool {
	return false
}

// 分片元数据结构V1版本
type partMetadataV1 struct {
	Version  string   `json:"version"`  // 元数据版本
	Size     int64    `json:"Size"`     // 总大小
	BlockIDs []string `json:"blockIDs"` // 分片ID列表
	ETag     string   `json:"etag"`     // ETag
}

// 创建新的分片元数据
func newPartMetaV1(uploadID string, partID int) (partMeta *partMetadataV1) {
	return &partMetadataV1{
		Version:  partMetaVersionV1,
		BlockIDs: []string{},
	}
}

// 生成上传ID
func getOCIUploadID() (string, error) {
	var id [8]byte

	n, err := rand.Read(id[:])
	if err != nil {
		return "", err
	}
	if n != 8 {
		return "", fmt.Errorf("insufficient random data (got %d bytes, wanted %d)", n, 8)
	}

	return hex.EncodeToString(id[:]), nil
}

// 检查上传ID是否有效
func checkOCIUploadID(ctx context.Context, uploadID string) (err error) {
	if len(uploadID) != 16 {
		return minio.MalformedUploadID{
			UploadID: uploadID,
		}
	}

	if _, err = hex.DecodeString(uploadID); err != nil {
		return minio.MalformedUploadID{
			UploadID: uploadID,
		}
	}

	return nil
}

// 获取元数据对象名称
func getOCIMetadataObjectName(objectName, uploadID string) string {
	return fmt.Sprintf(metadataObjectNameTemplate, objectName, uploadID)
}

// 获取分片元数据名称
func getOCIMetadataPartName(objectName, uploadID string, partID int) string {
	return fmt.Sprintf(metadataPartNamePrefix+"/part.%d", objectName, uploadID, partID)
}

// 获取分片元数据前缀
func getOCIMetadataPartPrefix(objectName, uploadID string) string {
	return fmt.Sprintf(metadataPartNamePrefix, objectName, uploadID)
}

// ListMultipartUploads - 列出所有分片上传
func (o *ociObjects) ListMultipartUploads(ctx context.Context, bucket, prefix, keyMarker, uploadIDMarker, delimiter string, maxUploads int, storageAccountName string) (result minio.ListMultipartsInfo, err error) {
	logger.Info(fmt.Sprintf("列出分片上传 - Bucket: %s, Prefix: %s", bucket, prefix))

	// 创建列出请求
	req := objectstorage.ListMultipartUploadsRequest{
		NamespaceName: common.String(o.namespace),
		BucketName:    common.String(bucket),
		Limit:         common.Int(maxUploads),
	}

	// 发送请求
	resp, err := o.Client.ListMultipartUploads(ctx, req)
	if err != nil {
		logger.Error(fmt.Sprintf("列出分片上传失败: %v", err))
		return result, ociToObjectError(err, bucket)
	}

	// 转换结果
	result.MaxUploads = maxUploads
	result.KeyMarker = keyMarker
	result.Prefix = prefix
	result.Delimiter = delimiter

	for _, upload := range resp.Items {
		if upload.Object == nil || upload.UploadId == nil {
			continue
		}

		// 手动应用prefix和delimiter过滤
		if prefix != "" && !strings.HasPrefix(*upload.Object, prefix) {
			continue
		}
		if delimiter != "" {
			parts := strings.Split(strings.TrimPrefix(*upload.Object, prefix), delimiter)
			if len(parts) > 1 {
				result.CommonPrefixes = append(result.CommonPrefixes, prefix+parts[0]+delimiter)
				continue
			}
		}

		result.Uploads = append(result.Uploads, minio.MultipartInfo{
			Object:    *upload.Object,
			UploadID:  *upload.UploadId,
			Initiated: upload.TimeCreated.Time,
		})
	}

	result.IsTruncated = resp.OpcNextPage != nil
	if result.IsTruncated {
		result.NextKeyMarker = *resp.OpcNextPage
	}

	logger.Info(fmt.Sprintf("列出分片上传成功 - 上传数量: %d", len(result.Uploads)))
	return result, nil
}

// NewMultipartUpload - 初始化分片上传
func (o *ociObjects) NewMultipartUpload(ctx context.Context, bucket, object string, opts minio.ObjectOptions, storageAccountName string) (uploadID string, err error) {
	logger.Info(fmt.Sprintf("初始化分片上传 - Bucket: %s, Object: %s", bucket, object))

	// 创建分片上传请求
	req := objectstorage.CreateMultipartUploadRequest{
		NamespaceName: common.String(o.namespace),
		BucketName:    common.String(bucket),
		CreateMultipartUploadDetails: objectstorage.CreateMultipartUploadDetails{
			Object: common.String(object),
		},
	}

	// 发送请求
	resp, err := o.Client.CreateMultipartUpload(ctx, req)
	if err != nil {
		logger.Error(fmt.Sprintf("创建分片上传失败: %v", err))
		return "", ociToObjectError(err, bucket, object)
	}

	if resp.UploadId == nil {
		return "", fmt.Errorf("upload ID is nil")
	}

	uploadID = *resp.UploadId

	// 创建元数据对象
	metaObjectName := getOCIMetadataObjectName(object, uploadID)
	metaReq := objectstorage.PutObjectRequest{
		NamespaceName: common.String(o.namespace),
		BucketName:    common.String(bucket),
		ObjectName:    common.String(metaObjectName),
		PutObjectBody: io.NopCloser(strings.NewReader("{}")), // 使用 io.NopCloser 包装
		ContentLength: common.Int64(2),                       // "{}" 的长度
	}

	_, err = o.Client.PutObject(ctx, metaReq)
	if err != nil {
		logger.Error(fmt.Sprintf("创建元数据对象失败: %v", err))
		return "", ociToObjectError(err, bucket, object)
	}

	logger.Info(fmt.Sprintf("分片上传初始化成功 - UploadID: %s", uploadID))
	return uploadID, nil
}

// PutObjectPart - 上传分片
func (o *ociObjects) PutObjectPart(ctx context.Context, bucket, object, uploadID string, partID int, r *minio.PutObjReader, opts minio.ObjectOptions, storageAccountName string) (info minio.PartInfo, err error) {
	logger.Info(fmt.Sprintf("上传分片 - Bucket: %s, Object: %s, UploadID: %s, PartNumber: %d", bucket, object, uploadID, partID))

	data := r.Reader
	if err = o.checkUploadIDExists(ctx, bucket, object, uploadID); err != nil {
		logger.Error(fmt.Sprintf("检查上传ID失败: %v", err))
		return info, err
	}

	// 创建分片上传请求
	req := objectstorage.UploadPartRequest{
		NamespaceName:  common.String(o.namespace),
		BucketName:     common.String(bucket),
		ObjectName:     common.String(object),
		UploadId:       common.String(uploadID),
		UploadPartNum:  common.Int(partID),
		UploadPartBody: data,
		ContentLength:  common.Int64(data.Size()),
	}

	// 发送请求
	resp, err := o.Client.UploadPart(ctx, req)
	if err != nil {
		logger.Error(fmt.Sprintf("上传分片失败: %v", err))
		return info, ociToObjectError(err, bucket, object)
	}

	// 获取ETag
	var etag string
	if resp.ETag != nil {
		etag = strings.Trim(*resp.ETag, "\"")
	}

	logger.Info(fmt.Sprintf("分片上传成功 - PartNumber: %d, ETag: %s", partID, etag))

	return minio.PartInfo{
		PartNumber:   partID,
		LastModified: time.Now(),
		ETag:         etag,
		Size:         data.Size(),
	}, nil
}

// ListObjectParts - 列出分片
func (o *ociObjects) ListObjectParts(ctx context.Context, bucket, object, uploadID string, partNumberMarker int, maxParts int, opts minio.ObjectOptions, storageAccountName string) (result minio.ListPartsInfo, err error) {
	logger.Info(fmt.Sprintf("列出分片 - Bucket: %s, Object: %s, UploadID: %s", bucket, object, uploadID))

	// 创建列出分片请求
	req := objectstorage.ListMultipartUploadPartsRequest{
		NamespaceName: common.String(o.namespace),
		BucketName:    common.String(bucket),
		ObjectName:    common.String(object),
		UploadId:      common.String(uploadID),
		Page:          common.String(fmt.Sprintf("%d", partNumberMarker)),
		Limit:         common.Int(maxParts),
	}

	// 发送请求
	resp, err := o.Client.ListMultipartUploadParts(ctx, req)
	if err != nil {
		logger.Error(fmt.Sprintf("列出分片失败: %v", err))
		return result, ociToObjectError(err, bucket, object)
	}

	// 预分配切片容量
	parts := make([]minio.PartInfo, 0, len(resp.Items))
	for _, part := range resp.Items {
		if part.PartNumber == nil || part.Etag == nil || part.Size == nil {
			continue
		}
		parts = append(parts, minio.PartInfo{
			PartNumber:   *part.PartNumber,
			LastModified: time.Now().UTC(),
			ETag:         strings.Trim(*part.Etag, "\""),
			Size:         *part.Size,
		})
	}

	result.Bucket = bucket
	result.Object = object
	result.UploadID = uploadID
	result.PartNumberMarker = partNumberMarker
	result.Parts = parts
	result.IsTruncated = resp.OpcNextPage != nil
	if result.IsTruncated {
		result.NextPartNumberMarker = partNumberMarker + len(parts)
	}

	logger.Info(fmt.Sprintf("列出分片成功 - 分片数量: %d", len(parts)))
	return result, nil
}

// CompleteMultipartUpload - 完成分片上传
func (o *ociObjects) CompleteMultipartUpload(ctx context.Context, bucket, object, uploadID string, uploadedParts []minio.CompletePart, opts minio.ObjectOptions, storageAccountName string) (objInfo minio.ObjectInfo, err error) {
	logger.Info(fmt.Sprintf("完成分片上传 - Bucket: %s, Object: %s, UploadID: %s", bucket, object, uploadID))

	// 按分片号排序
	sort.Slice(uploadedParts, func(i, j int) bool {
		return uploadedParts[i].PartNumber < uploadedParts[j].PartNumber
	})

	// 预分配切片容量
	partsToCommit := make([]objectstorage.CommitMultipartUploadPartDetails, 0, len(uploadedParts))
	for _, part := range uploadedParts {
		partsToCommit = append(partsToCommit, objectstorage.CommitMultipartUploadPartDetails{
			PartNum: common.Int(part.PartNumber),
			Etag:    common.String(part.ETag),
		})
	}

	// 创建提交请求
	req := objectstorage.CommitMultipartUploadRequest{
		NamespaceName: common.String(o.namespace),
		BucketName:    common.String(bucket),
		ObjectName:    common.String(object),
		UploadId:      common.String(uploadID),
		CommitMultipartUploadDetails: objectstorage.CommitMultipartUploadDetails{
			PartsToCommit: partsToCommit,
		},
	}

	// 发送请求
	resp, err := o.Client.CommitMultipartUpload(ctx, req)
	if err != nil {
		logger.Error(fmt.Sprintf("完成分片上传失败: %v", err))
		return objInfo, ociToObjectError(err, bucket, object)
	}

	// 获取最终对象的ETag
	var finalETag string
	if resp.ETag != nil {
		finalETag = strings.Trim(*resp.ETag, "\"")
	}

	// 获取最终对象的大小
	var totalSize int64
	headResp, err := o.Client.HeadObject(ctx, objectstorage.HeadObjectRequest{
		NamespaceName: common.String(o.namespace),
		BucketName:    common.String(bucket),
		ObjectName:    common.String(object),
	})
	if err == nil && headResp.ContentLength != nil {
		totalSize = *headResp.ContentLength
	}

	logger.Info(fmt.Sprintf("分片上传完成 - ETag: %s, Size: %d", finalETag, totalSize))

	return minio.ObjectInfo{
		Bucket:      bucket,
		Name:        object,
		ModTime:     time.Now(),
		Size:        totalSize,
		ETag:        finalETag,
		ContentType: getContentType(object),
		UserDefined: opts.UserDefined,
	}, nil
}

// AbortMultipartUpload - 中止分片上传
func (o *ociObjects) AbortMultipartUpload(ctx context.Context, bucket, object, uploadID string, opts minio.ObjectOptions, storageAccountName string) error {
	logger.Info(fmt.Sprintf("中止分片上传 - Bucket: %s, Object: %s, UploadID: %s", bucket, object, uploadID))

	// 创建中止请求
	req := objectstorage.AbortMultipartUploadRequest{
		NamespaceName: common.String(o.namespace),
		BucketName:    common.String(bucket),
		ObjectName:    common.String(object),
		UploadId:      common.String(uploadID),
	}

	// 发送请求
	_, err := o.Client.AbortMultipartUpload(ctx, req)
	if err != nil {
		logger.Error(fmt.Sprintf("中止分片上传失败: %v", err))
		return ociToObjectError(err, bucket, object)
	}

	logger.Info("分片上传已中止")
	return nil
}

// 根据文件名获取内容类型
func getContentType(name string) string {
	ext := filepath.Ext(name)
	switch ext {
	case ".html", ".htm":
		return "text/html"
	case ".css":
		return "text/css"
	case ".js":
		return "application/javascript"
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".pdf":
		return "application/pdf"
	case ".txt":
		return "text/plain"
	case ".mp4":
		return "video/mp4"
	case ".mp3":
		return "audio/mpeg"
	case ".json":
		return "application/json"
	case ".xml":
		return "application/xml"
	case ".zip":
		return "application/zip"
	case ".tar":
		return "application/x-tar"
	case ".gz", ".gzip":
		return "application/gzip"
	default:
		return "application/octet-stream"
	}
}

// GetBucketPolicy - 获取存储桶策略
func (o *ociObjects) GetBucketPolicy(ctx context.Context, bucket string, storageAccountName string) (*policy.Policy, error) {
	// 获取存储桶策略请求
	req := objectstorage.GetBucketRequest{
		NamespaceName: common.String(o.namespace),
		BucketName:    common.String(bucket),
	}

	// 发送请求
	resp, err := o.Client.GetBucket(ctx, req)
	if err != nil {
		logger.LogIf(ctx, err)
		return nil, ociToObjectError(err, bucket)
	}

	var policyInfo = &policy.Policy{
		Version: policy.DefaultVersion,
	}

	// 根据OCI公共访问类型设置策略
	switch resp.PublicAccessType {
	case "NoPublicAccess":
		// 私有访问，不需要添加任何策略
		return policyInfo, nil
	case "ObjectRead":
		// 公共读取访问
		readOnlyStmt := policy.Statement{
			Actions: policy.NewActionSet(
				policy.GetObjectAction,
				policy.ListBucketAction,
			),
			Effect:    policy.Allow,
			Principal: policy.NewPrincipal("*"),
			Resources: policy.NewResourceSet(
				policy.NewResource(bucket, ""),
				policy.NewResource(bucket, "*"),
			),
		}
		policyInfo.Statements = []policy.Statement{readOnlyStmt}
		return policyInfo, nil
	case "ObjectReadWithoutList":
		// 公共读取但不列出
		readOnlyStmt := policy.Statement{
			Actions: policy.NewActionSet(
				policy.GetObjectAction,
			),
			Effect:    policy.Allow,
			Principal: policy.NewPrincipal("*"),
			Resources: policy.NewResourceSet(
				policy.NewResource(bucket, "*"),
			),
		}
		policyInfo.Statements = []policy.Statement{readOnlyStmt}
		return policyInfo, nil
	default:
		logger.LogIf(ctx, fmt.Errorf("unsupported bucket public access type: %s", resp.PublicAccessType))
		return nil, minio.NotImplemented{}
	}
}

// GetMultipartInfo - 获取分片上传信息
func (o *ociObjects) GetMultipartInfo(ctx context.Context, bucket, object, uploadID string, opts minio.ObjectOptions, storageAccountName string) (result minio.MultipartInfo, err error) {
	logger.Info(fmt.Sprintf("获取分片上传信息 - Bucket: %s, Object: %s, UploadID: %s", bucket, object, uploadID))

	// 使用ListMultipartUploadParts来获取上传信息
	req := objectstorage.ListMultipartUploadPartsRequest{
		NamespaceName: common.String(o.namespace),
		BucketName:    common.String(bucket),
		ObjectName:    common.String(object),
		UploadId:      common.String(uploadID),
		Limit:         common.Int(1), // 只需要获取基本信息
	}

	// 发送请求
	_, err = o.Client.ListMultipartUploadParts(ctx, req)
	if err != nil {
		logger.Error(fmt.Sprintf("获取分片上传信息失败: %v", err))
		return result, ociToObjectError(err, bucket, object)
	}

	// 构造返回结果
	result = minio.MultipartInfo{
		Bucket:    bucket,
		Object:    object,
		UploadID:  uploadID,
		Initiated: time.Now(), // 由于没有直接获取创建时间的API，使用当前时间
	}

	logger.Info("获取分片上传信息成功")
	return result, nil
}

// 检查上传ID是否存在
func (o *ociObjects) checkUploadIDExists(ctx context.Context, bucket, object, uploadID string) error {
	// 获取元数据对象名称
	metaObjectName := getOCIMetadataObjectName(object, uploadID)

	// 检查元数据对象是否存在
	_, err := o.Client.HeadObject(ctx, objectstorage.HeadObjectRequest{
		NamespaceName: common.String(o.namespace),
		BucketName:    common.String(bucket),
		ObjectName:    common.String(metaObjectName),
	})
	if err != nil {
		if serviceErr, ok := err.(common.ServiceError); ok && serviceErr.GetHTTPStatusCode() == http.StatusNotFound {
			return minio.InvalidUploadID{
				UploadID: uploadID,
			}
		}
		return ociToObjectError(err, bucket, object)
	}
	return nil
}
