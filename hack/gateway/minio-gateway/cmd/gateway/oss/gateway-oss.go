/*
 * MinIO Object Storage (c) 2021 MinIO, Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package oss

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"io"
	"net/http"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	minio "seawave-gateway/cmd"
	"seawave-gateway/internal/logger"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/dustin/go-humanize"
	"github.com/minio/cli"
	"github.com/minio/madmin-go"
	"github.com/minio/pkg/bucket/policy"
	"github.com/minio/pkg/env"
)

const (
	ossDefaultUploadPartSize   = 20 * humanize.MiByte
	ossUploadRetryAttempts     = 5
	ossS3MinPartSize           = 5 * humanize.MiByte
	metadataObjectNameTemplate = minio.GatewayMinioSysTmp + "multipart/v1/%s.%x/oss.json"
	ossMarkerPrefix            = "{minio}"
	metadataPartNamePrefix     = minio.GatewayMinioSysTmp + "multipart/v1/%s.%x"
	maxPartsCount              = 10000
	partMetaVersionV1          = "v1"
)

var (
	ossUploadPartSize    int
	ossUploadConcurrency int
)

func init() {
	const ossGatewayTemplate = `NAME:
  {{.HelpName}} - {{.Usage}}

USAGE:
  {{.HelpName}} {{if .VisibleFlags}}[FLAGS]{{end}} [ENDPOINT]
{{if .VisibleFlags}}
FLAGS:
  {{range .VisibleFlags}}{{.}}
  {{end}}{{end}}
ENDPOINT:
  OSS server endpoint. Default ENDPOINT is https://oss-cn-hangzhou.aliyuncs.com

EXAMPLES:
  1. Start minio gateway server for Aliyun OSS backend.
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_ROOT_USER{{.AssignmentOperator}}ossaccesskeyid
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_ROOT_PASSWORD{{.AssignmentOperator}}ossaccesskeysecret
     {{.Prompt}} {{.HelpName}}

  2. Start minio gateway server for Aliyun OSS backend with edge caching enabled.
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_ROOT_USER{{.AssignmentOperator}}ossaccesskeyid
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_ROOT_PASSWORD{{.AssignmentOperator}}ossaccesskeysecret
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_CACHE_DRIVES{{.AssignmentOperator}}"/mnt/drive1,/mnt/drive2,/mnt/drive3,/mnt/drive4"
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_CACHE_EXCLUDE{{.AssignmentOperator}}"bucket1/*,*.png"
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_CACHE_QUOTA{{.AssignmentOperator}}90
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_CACHE_AFTER{{.AssignmentOperator}}3
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_CACHE_WATERMARK_LOW{{.AssignmentOperator}}75
     {{.Prompt}} {{.EnvVarSetCommand}} MINIO_CACHE_WATERMARK_HIGH{{.AssignmentOperator}}85
     {{.Prompt}} {{.HelpName}}
`

	minio.RegisterGatewayCommand(cli.Command{
		Name:               minio.OSSBackendGateway,
		Usage:              "Aliyun Object Storage Service (OSS)",
		Action:             ossGatewayMain,
		CustomHelpTemplate: ossGatewayTemplate,
		HideHelpCommand:    true,
	})
}

// Returns true if marker was returned by OSS, i.e prefixed with
// {minio}
func isOSSMarker(marker string) bool {
	return strings.HasPrefix(marker, ossMarkerPrefix)
}

// Handler for 'minio gateway oss' command line.
func ossGatewayMain(ctx *cli.Context) {
	// Validate gateway arguments.
	host := env.Get("ALICLOUD_HOST", "https://oss-cn-beijing.aliyuncs.com")
	serverAddr := ctx.GlobalString("address")
	if serverAddr == "" || serverAddr == ":"+minio.GlobalMinioDefaultPort {
		serverAddr = ctx.String("address")
	}
	// Validate gateway arguments.
	logger.FatalIf(minio.ValidateGatewayArguments(serverAddr, host), "Invalid argument")

	minio.StartGateway(ctx, &OSS{host})
}

// OSS implements Gateway.
type OSS struct {
	host string
}

// Name implements Gateway interface.
func (g *OSS) Name() string {
	return minio.OSSBackendGateway
}

// NewGatewayLayer initializes OSS client and returns OSSObjects.
func (g *OSS) NewGatewayLayer(creds madmin.Credentials) (minio.ObjectLayer, error) {
	var err error

	// Override credentials from the OSS storage environment variables if specified
	if acc, key := env.Get("ALICLOUD_ACCESS_KEY_ID", creds.AccessKey), env.Get("ALICLOUD_ACCESS_KEY_SECRET", creds.SecretKey); acc != "" && key != "" {
		creds = madmin.Credentials{
			AccessKey: acc,
			SecretKey: key,
		}
	}

	// Initialize OSS client
	client, err := oss.New(g.host, creds.AccessKey, creds.SecretKey)
	if err != nil {
		return nil, err
	}

	ossUploadPartSize, err = env.GetInt("MINIO_OSS_PART_SIZE_MB", 20)
	if err != nil {
		return nil, err
	}
	ossUploadPartSize *= humanize.MiByte
	if ossUploadPartSize <= 0 || ossUploadPartSize > 100*humanize.MiByte {
		return nil, fmt.Errorf("MINIO_OSS_PART_SIZE_MB should be an integer value between 0 and 100")
	}

	ossUploadConcurrency, err = env.GetInt("MINIO_OSS_UPLOAD_CONCURRENCY", 4)
	if err != nil {
		return nil, err
	}

	return &ossObjects{
		Client:     client,
		httpClient: &http.Client{},
		metrics:    minio.NewMetrics(),
	}, nil
}

// ossObjects - Implements Object layer for OSS blob storage.
type ossObjects struct {
	minio.GatewayUnsupported
	Client     *oss.Client
	httpClient *http.Client
	metrics    *minio.BackendMetrics
}

// Convert OSS errors to minio object layer errors.
func ossToObjectError(err error, params ...string) error {
	if err == nil {
		return nil
	}

	bucket := ""
	object := ""
	if len(params) >= 1 {
		bucket = params[0]
	}
	if len(params) == 2 {
		object = params[1]
	}
	logger.Error("oss_error: %v", err)

	ossErr, ok := err.(oss.ServiceError)
	if !ok {
		// We don't interpret non OSS errors. As OSS errors will
		// have StatusCode to help to convert to object errors.
		return err
	}

	switch ossErr.Code {
	case "NoSuchBucket":
		err = minio.BucketNotFound{Bucket: bucket}
	case "NoSuchKey":
		if object != "" {
			err = minio.ObjectNotFound{Bucket: bucket, Object: object}
		}
	case "InvalidBucketName":
		err = minio.BucketNameInvalid{Bucket: bucket}
	case "InvalidObjectName":
		err = minio.ObjectNameInvalid{Bucket: bucket, Object: object}
	case "BucketAlreadyExists":
		err = minio.BucketAlreadyExists{Bucket: bucket}
	case "BucketNotEmpty":
		err = minio.BucketNotEmpty{Bucket: bucket}
	//case "InvalidAccessKeyId":
	//	err = minio.InvalidAccessKeyID{Bucket: bucket}
	//case "SignatureDoesNotMatch":
	//	err = minio.SignatureDoesNotMatch{Bucket: bucket}
	case "AccessDenied":
		err = minio.PrefixAccessDenied{Bucket: bucket, Object: object}
	case "NoSuchUpload":
		err = minio.InvalidUploadID{Bucket: bucket, Object: object, UploadID: ""}
	}

	return err
}

// GetMetrics returns this gateway's metrics
func (o *ossObjects) GetMetrics(ctx context.Context) (*minio.BackendMetrics, error) {
	return o.metrics, nil
}

// Shutdown - save any gateway metadata to disk
// if necessary and reload upon next restart.
func (o *ossObjects) Shutdown(ctx context.Context) error {
	return nil
}

// StorageInfo - Not relevant to OSS backend.
func (o *ossObjects) StorageInfo(ctx context.Context) (si minio.StorageInfo, _ []error) {
	si.Backend.Type = madmin.Gateway
	si.Backend.GatewayOnline = true
	return si, nil
}

// MakeBucketWithLocation - Create a new container on OSS backend.
func (o *ossObjects) MakeBucketWithLocation(ctx context.Context, bucket string, opts minio.BucketOptions, storageAccountName string) error {
	err := o.Client.CreateBucket(bucket)
	if err != nil {
		return ossToObjectError(err, bucket)
	}
	return nil
}

// GetBucketInfo - Get bucket metadata.
func (o *ossObjects) GetBucketInfo(ctx context.Context, bucket string, storageAccountName string) (bi minio.BucketInfo, e error) {
	// Check if bucket exists
	_, err := o.Client.GetBucketInfo(bucket)
	if err != nil {
		return bi, ossToObjectError(err, bucket)
	}

	return minio.BucketInfo{
		Name:    bucket,
		Created: time.Now().UTC(),
	}, nil
}

// ListBuckets - Lists all OSS buckets, uses OSS equivalent ListBuckets.
func (o *ossObjects) ListBuckets(ctx context.Context, storageAccountName string) (buckets []minio.BucketInfo, err error) {
	ossBuckets, err := o.Client.ListBuckets()
	if err != nil {
		return nil, ossToObjectError(err)
	}

	for _, bucket := range ossBuckets.Buckets {
		buckets = append(buckets, minio.BucketInfo{
			Name:    bucket.Name,
			Created: bucket.CreationDate,
		})
	}

	return buckets, nil
}

// DeleteBucket - delete a container on OSS.
func (o *ossObjects) DeleteBucket(ctx context.Context, bucket string, opts minio.DeleteBucketOptions, storageAccountName string) error {
	err := o.Client.DeleteBucket(bucket)
	if err != nil {
		return ossToObjectError(err, bucket)
	}
	return nil
}

// ListObjects - lists all objects on OSS with in a container filtered by prefix
// and marker, uses OSS equivalent ListObjects.
func (o *ossObjects) ListObjects(ctx context.Context, bucket, prefix, marker, delimiter string, maxKeys int, storageAccountName string) (result minio.ListObjectsInfo, err error) {
	buck, err := o.Client.Bucket(bucket)
	if err != nil {
		return result, ossToObjectError(err, bucket)
	}

	// If marker is not empty, check if it is properly formatted
	// by minio before proceeding
	if marker != "" {
		if strings.HasPrefix(marker, ossMarkerPrefix) {
			marker = strings.TrimPrefix(marker, ossMarkerPrefix)
		}
	}

	// 使用正确的OSS ListObject选项
	ossObjects, err := buck.ListObjects(oss.Prefix(prefix), oss.Marker(marker),
		oss.Delimiter(delimiter), oss.MaxKeys(maxKeys))
	if err != nil {
		return result, ossToObjectError(err, bucket)
	}

	result.IsTruncated = ossObjects.IsTruncated
	result.NextMarker = ossMarkerPrefix + ossObjects.NextMarker

	// Populate objects
	for _, obj := range ossObjects.Objects {
		if obj.Key == "" {
			continue
		}
		// 使用自定义函数获取内容类型
		contentType := getContentType(obj.Key)
		result.Objects = append(result.Objects, minio.ObjectInfo{
			Bucket:          bucket,
			Name:            obj.Key,
			ModTime:         obj.LastModified,
			Size:            obj.Size,
			ETag:            strings.Trim(obj.ETag, "\""),
			ContentType:     contentType,
			ContentEncoding: "", // OSS对象不直接提供ContentEncoding
		})
	}

	// Populate prefixes
	result.Prefixes = ossObjects.CommonPrefixes

	return result, nil
}

// ListObjectsV2 - list all objects in OSS bucket filtered by prefix
func (o *ossObjects) ListObjectsV2(ctx context.Context, bucket, prefix, continuationToken, delimiter string, maxKeys int, fetchOwner bool, startAfter string, storageAccountName string) (result minio.ListObjectsV2Info, err error) {
	marker := continuationToken
	if marker == "" {
		marker = startAfter
	}
	resultV1, err := o.ListObjects(ctx, bucket, prefix, marker, delimiter, maxKeys, storageAccountName)
	if err != nil {
		return result, err
	}

	result.IsTruncated = resultV1.IsTruncated
	result.ContinuationToken = continuationToken
	result.NextContinuationToken = resultV1.NextMarker
	result.Objects = resultV1.Objects
	result.Prefixes = resultV1.Prefixes
	return result, nil
}

// GetObjectNInfo - returns object info and locked object ReadCloser
func (o *ossObjects) GetObjectNInfo(ctx context.Context, bucket, object string, rs *minio.HTTPRangeSpec, h http.Header, lockType minio.LockType, opts minio.ObjectOptions, storageAccountName string) (gr *minio.GetObjectReader, err error) {
	var objInfo minio.ObjectInfo
	objInfo, err = o.GetObjectInfo(ctx, bucket, object, opts, storageAccountName)
	if err != nil {
		return nil, err
	}

	var startOffset, length int64
	startOffset, length, err = rs.GetOffsetLength(objInfo.Size)
	if err != nil {
		return nil, err
	}

	pr, pw := io.Pipe()
	go func() {
		err := o.getObject(ctx, bucket, object, startOffset, length, pw, objInfo.ETag, opts)
		pw.CloseWithError(err)
	}()
	// Setup cleanup function to cause the above go-routine to
	// exit in case of partial read
	pipeCloser := func() { pr.Close() }
	return minio.NewGetObjectReaderFromReader(pr, objInfo, opts, pipeCloser)
}

// GetObject - reads an object from OSS. Supports additional parameters like offset and length
// which are synonymous with HTTP Range requests.
func (o *ossObjects) getObject(ctx context.Context, bucket, object string, startOffset int64, length int64, writer io.Writer, etag string, opts minio.ObjectOptions) error {
	buck, err := o.Client.Bucket(bucket)
	if err != nil {
		return ossToObjectError(err, bucket, object)
	}

	var ossOpts []oss.Option
	if startOffset >= 0 && length >= 0 {
		ossOpts = append(ossOpts, oss.Range(startOffset, startOffset+length-1))
	}

	object = strings.TrimPrefix(object, "/")
	reader, err := buck.GetObject(object, ossOpts...)
	if err != nil {
		return ossToObjectError(err, bucket, object)
	}
	defer reader.Close()

	_, err = io.Copy(writer, reader)
	return err
}

// GetObjectInfo - reads object info and replies back ObjectInfo
func (o *ossObjects) GetObjectInfo(ctx context.Context, bucket, object string, opts minio.ObjectOptions, storageAccountName string) (objInfo minio.ObjectInfo, err error) {
	buck, err := o.Client.Bucket(bucket)
	if err != nil {
		return objInfo, ossToObjectError(err, bucket, object)
	}

	object = strings.TrimPrefix(object, "/")
	header, err := buck.GetObjectMeta(object)
	if err != nil {
		return objInfo, ossToObjectError(err, bucket, object)
	}

	// Guess content-type from the extension if not present.
	contentType := header.Get("Content-Type")
	if contentType == "" {
		contentType = getContentType(object)
	}

	size, _ := strconv.ParseInt(header.Get("Content-Length"), 10, 64)
	modTime, _ := http.ParseTime(header.Get("Last-Modified"))

	objInfo = minio.ObjectInfo{
		Bucket:          bucket,
		Name:            object,
		ModTime:         modTime,
		Size:            size,
		ETag:            strings.Trim(header.Get("ETag"), "\""),
		ContentType:     contentType,
		ContentEncoding: header.Get("Content-Encoding"),
	}

	return objInfo, nil
}

// PutObject - Create a new object with the incoming data
func (o *ossObjects) PutObject(ctx context.Context, bucket, object string, r *minio.PutObjReader, opts minio.ObjectOptions, storageAccountName string) (objInfo minio.ObjectInfo, err error) {
	data := r.Reader

	buck, err := o.Client.Bucket(bucket)
	if err != nil {
		return objInfo, ossToObjectError(err, bucket, object)
	}

	object = strings.TrimPrefix(object, "/")

	// Get content-type which will be saved with object
	contentType := opts.UserDefined["content-type"]
	if contentType == "" {
		contentType = getContentType(object)
	}

	ossOpts := []oss.Option{
		oss.ContentType(contentType),
	}

	err = buck.PutObject(object, data, ossOpts...)
	if err != nil {
		return objInfo, ossToObjectError(err, bucket, object)
	}

	header, err := buck.GetObjectMeta(object)
	if err != nil {
		return objInfo, ossToObjectError(err, bucket, object)
	}

	size, _ := strconv.ParseInt(header.Get("Content-Length"), 10, 64)
	modTime, _ := http.ParseTime(header.Get("Last-Modified"))

	return minio.ObjectInfo{
		Bucket:      bucket,
		Name:        object,
		ModTime:     modTime,
		Size:        size,
		ETag:        strings.Trim(header.Get("ETag"), "\""),
		ContentType: contentType,
	}, nil
}

// CopyObject - Copies a blob from source container to destination container.
func (o *ossObjects) CopyObject(ctx context.Context, srcBucket, srcObject, destBucket, destObject string, srcInfo minio.ObjectInfo, srcOpts, dstOpts minio.ObjectOptions, storageAccountName string) (objInfo minio.ObjectInfo, err error) {
	_, err = o.Client.Bucket(srcBucket)
	if err != nil {
		return objInfo, ossToObjectError(err, srcBucket, srcObject)
	}

	destBuck, err := o.Client.Bucket(destBucket)
	if err != nil {
		return objInfo, ossToObjectError(err, destBucket, destObject)
	}

	srcObject = strings.TrimPrefix(srcObject, "/")
	destObject = strings.TrimPrefix(destObject, "/")

	// Copy source object to destination
	_, err = destBuck.CopyObjectFrom(srcBucket, srcObject, destObject, []oss.Option{}...)
	if err != nil {
		return objInfo, ossToObjectError(err, destBucket, destObject)
	}

	header, err := destBuck.GetObjectMeta(destObject)
	if err != nil {
		return objInfo, ossToObjectError(err, destBucket, destObject)
	}

	size, _ := strconv.ParseInt(header.Get("Content-Length"), 10, 64)
	modTime, _ := http.ParseTime(header.Get("Last-Modified"))

	return minio.ObjectInfo{
		Bucket:      destBucket,
		Name:        destObject,
		ModTime:     modTime,
		Size:        size,
		ETag:        strings.Trim(header.Get("ETag"), "\""),
		ContentType: header.Get("Content-Type"),
	}, nil
}

// DeleteObject - Deletes a blob on OSS
func (o *ossObjects) DeleteObject(ctx context.Context, bucket, object string, opts minio.ObjectOptions, storageAccountName string) (minio.ObjectInfo, error) {
	buck, err := o.Client.Bucket(bucket)
	if err != nil {
		return minio.ObjectInfo{}, ossToObjectError(err, bucket, object)
	}

	object = strings.TrimPrefix(object, "/")
	err = buck.DeleteObject(object)
	if err != nil {
		return minio.ObjectInfo{}, ossToObjectError(err, bucket, object)
	}

	return minio.ObjectInfo{
		Bucket: bucket,
		Name:   object,
	}, nil
}

// DeleteObjects - Deletes multiple objects in OSS
func (o *ossObjects) DeleteObjects(ctx context.Context, bucket string, objects []minio.ObjectToDelete, opts minio.ObjectOptions, storageAccountName string) ([]minio.DeletedObject, []error) {
	errs := make([]error, len(objects))
	dobjects := make([]minio.DeletedObject, len(objects))

	buck, err := o.Client.Bucket(bucket)
	if err != nil {
		for idx := range errs {
			errs[idx] = ossToObjectError(err, bucket, objects[idx].ObjectName)
		}
		return dobjects, errs
	}

	// Create slice of object keys to delete
	names := make([]string, len(objects))
	for idx, obj := range objects {
		names[idx] = strings.TrimPrefix(obj.ObjectName, "/")
	}

	// Delete objects in bulk
	_, err = buck.DeleteObjects(names)
	if err != nil {
		ossErr := ossToObjectError(err, bucket)
		for idx := range errs {
			errs[idx] = ossErr
		}
		return dobjects, errs
	}

	// Fill deleted objects
	for idx := range objects {
		dobjects[idx] = minio.DeletedObject{
			ObjectName: objects[idx].ObjectName,
		}
	}

	return dobjects, errs
}

// IsCompressionSupported returns whether compression is applicable for this layer.
func (o *ossObjects) IsCompressionSupported() bool {
	return false
}

// IsNotificationSupported returns whether notifications are applicable for this layer.
func (o *ossObjects) IsNotificationSupported() bool {
	return false
}

// partMetadataV1 - metadata for multipart upload operations.
type partMetadataV1 struct {
	Version  string   `json:"version"`
	Size     int64    `json:"Size"`
	ETag     string   `json:"etag"`
	BlockIDs []string `json:"blockIDs"`
}

// Returns the initialized part metadata
func newPartMetaV1(uploadID string, partID int) (partMeta *partMetadataV1) {
	return &partMetadataV1{
		Version:  partMetaVersionV1,
		BlockIDs: []string{},
	}
}

// getOSSUploadID - returns new upload ID which is hex encoded 8 bytes random value.
func getOSSUploadID() (string, error) {
	var id [8]byte

	n, err := rand.Read(id[:])
	if err != nil {
		return "", err
	}
	if n != 8 {
		return "", fmt.Errorf("insufficient random data (got %d bytes, wanted %d)", n, 8)
	}

	return hex.EncodeToString(id[:]), nil
}

// checkOSSUploadID - returns error in case of given string is upload ID.
func checkOSSUploadID(ctx context.Context, uploadID string) (err error) {
	if len(uploadID) != 16 {
		return minio.MalformedUploadID{
			UploadID: uploadID,
		}
	}

	if _, err = hex.DecodeString(uploadID); err != nil {
		return minio.MalformedUploadID{
			UploadID: uploadID,
		}
	}

	return nil
}

// getOSSMetadataObjectName returns the metadata object name for the multipart upload.
func getOSSMetadataObjectName(objectName, uploadID string) string {
	return fmt.Sprintf(metadataObjectNameTemplate, objectName, uploadID)
}

// getOSSMetadataPartName returns the metadata part object name for the multipart upload.
func getOSSMetadataPartName(objectName, uploadID string, partID int) string {
	return fmt.Sprintf(metadataPartNamePrefix+"/part.%d", objectName, uploadID, partID)
}

// getOSSMetadataPartPrefix returns the prefix of part metadata objects.
func getOSSMetadataPartPrefix(objectName, uploadID string) string {
	return fmt.Sprintf(metadataPartNamePrefix, objectName, uploadID)
}

// ListMultipartUploads - lists all multipart uploads.
func (o *ossObjects) ListMultipartUploads(ctx context.Context, bucket, prefix, keyMarker, uploadIDMarker, delimiter string, maxUploads int, storageAccountName string) (result minio.ListMultipartsInfo, err error) {
	buck, err := o.Client.Bucket(bucket)
	if err != nil {
		return result, ossToObjectError(err, bucket)
	}

	// Remove the trailing slash if any
	keyMarker = strings.TrimSuffix(keyMarker, "/")
	prefix = strings.TrimSuffix(prefix, "/")

	// Validate upload id.
	if uploadIDMarker != "" {
		if err := checkOSSUploadID(ctx, uploadIDMarker); err != nil {
			return result, err
		}
	}

	maxUploads = maxUploads + 1000 // Add extra 1000 to retrieve more and filter out later.

	// Get list of multipart uploads with marker
	uploads, err := buck.ListMultipartUploads(oss.Prefix(prefix), oss.KeyMarker(keyMarker),
		oss.Delimiter(delimiter), oss.MaxUploads(maxUploads))
	if err != nil {
		return result, ossToObjectError(err, bucket)
	}

	result.MaxUploads = maxUploads
	result.KeyMarker = keyMarker
	result.Prefix = prefix
	result.Delimiter = delimiter

	// Populate the multipart uploads.
	for _, upload := range uploads.Uploads {
		if strings.HasSuffix(upload.Key, "/") {
			continue
		}
		result.Uploads = append(result.Uploads, minio.MultipartInfo{
			Object:    upload.Key,
			UploadID:  upload.UploadID,
			Initiated: upload.Initiated,
		})
	}

	result.NextKeyMarker = uploads.NextKeyMarker
	result.IsTruncated = uploads.IsTruncated

	// Populate common prefixes.
	for _, prefix := range uploads.CommonPrefixes {
		result.CommonPrefixes = append(result.CommonPrefixes, prefix)
	}

	return result, nil
}

// NewMultipartUpload - initializes a new multipart upload.
func (o *ossObjects) NewMultipartUpload(ctx context.Context, bucket, object string, opts minio.ObjectOptions, storageAccountName string) (uploadID string, err error) {
	buck, err := o.Client.Bucket(bucket)
	if err != nil {
		logger.Error("获取bucket失败: %v", err)
		return "", ossToObjectError(err, bucket, object)
	}

	// 获取内容类型
	contentType := opts.UserDefined["content-type"]
	if contentType == "" {
		contentType = getContentType(object)
	}

	// 使用OSS原生接口初始化分片上传
	imur, err := buck.InitiateMultipartUpload(object, oss.ContentType(contentType))
	if err != nil {
		logger.Error("初始化分片上传失败: %v", err)
		return "", ossToObjectError(err, bucket, object)
	}

	logger.Info("成功初始化分片上传: bucket=%s, object=%s, uploadID=%s", bucket, object, imur.UploadID)
	return imur.UploadID, nil
}

// PutObjectPart - uploads a part in a multipart upload.
func (o *ossObjects) PutObjectPart(ctx context.Context, bucket, object, uploadID string, partID int, r *minio.PutObjReader, opts minio.ObjectOptions, storageAccountName string) (info minio.PartInfo, err error) {
	if uploadID == "" {
		logger.Error("无效的uploadID: uploadID为空")
		return info, minio.InvalidUploadID{
			UploadID: uploadID,
		}
	}

	buck, err := o.Client.Bucket(bucket)
	if err != nil {
		logger.Error("获取bucket失败: %v", err)
		return info, ossToObjectError(err, bucket, object)
	}

	// 使用OSS原生接口上传分片
	part, err := buck.UploadPart(oss.InitiateMultipartUploadResult{
		Bucket:   bucket,
		Key:      object,
		UploadID: uploadID,
	}, r.Reader, r.Reader.Size(), partID)
	if err != nil {
		logger.Error("上传分片失败: bucket=%s, object=%s, uploadID=%s, partID=%d, error=%v", bucket, object, uploadID, partID, err)
		return info, ossToObjectError(err, bucket, object)
	}

	logger.Info("成功上传分片: bucket=%s, object=%s, uploadID=%s, partID=%d", bucket, object, uploadID, partID)
	return minio.PartInfo{
		PartNumber:   partID,
		LastModified: time.Now().UTC(),
		ETag:         strings.Trim(part.ETag, "\""),
		Size:         r.Reader.Size(),
	}, nil
}

// GetMultipartInfo returns multipart info of the uploadId of the object
func (o *ossObjects) GetMultipartInfo(ctx context.Context, bucket, object, uploadID string, opts minio.ObjectOptions, storageAccountName string) (result minio.MultipartInfo, err error) {
	buck, err := o.Client.Bucket(bucket)
	if err != nil {
		return result, ossToObjectError(err, bucket, object)
	}

	// 使用OSS原生接口查询分片上传信息
	_, err = buck.ListUploadedParts(oss.InitiateMultipartUploadResult{
		Bucket:   bucket,
		Key:      object,
		UploadID: uploadID,
	}, oss.MaxParts(1))
	if err != nil {
		return result, ossToObjectError(err, bucket, object)
	}

	return minio.MultipartInfo{
		Object:    object,
		UploadID:  uploadID,
		Initiated: time.Now().UTC(),
	}, nil
}

// ListObjectParts - lists all previously uploaded parts for a given object and upload ID.
func (o *ossObjects) ListObjectParts(ctx context.Context, bucket, object, uploadID string, partNumberMarker int, maxParts int, opts minio.ObjectOptions, storageAccountName string) (result minio.ListPartsInfo, err error) {
	buck, err := o.Client.Bucket(bucket)
	if err != nil {
		return result, ossToObjectError(err, bucket, object)
	}

	// 使用OSS原生接口列出分片
	parts, err := buck.ListUploadedParts(oss.InitiateMultipartUploadResult{
		Bucket:   bucket,
		Key:      object,
		UploadID: uploadID,
	}, oss.MaxParts(maxParts), oss.PartNumberMarker(partNumberMarker))
	if err != nil {
		return result, ossToObjectError(err, bucket, object)
	}

	result.Bucket = bucket
	result.Object = object
	result.UploadID = uploadID
	result.PartNumberMarker = partNumberMarker
	result.IsTruncated = parts.IsTruncated
	if result.IsTruncated {
		nextMarker, _ := strconv.Atoi(parts.NextPartNumberMarker)
		result.NextPartNumberMarker = nextMarker
	}

	// 转换分片信息
	for _, part := range parts.UploadedParts {
		result.Parts = append(result.Parts, minio.PartInfo{
			PartNumber:   part.PartNumber,
			LastModified: part.LastModified,
			ETag:         strings.Trim(part.ETag, "\""),
			Size:         int64(part.Size),
		})
	}

	return result, nil
}

// AbortMultipartUpload - aborts a multipart upload.
func (o *ossObjects) AbortMultipartUpload(ctx context.Context, bucket, object, uploadID string, opts minio.ObjectOptions, storageAccountName string) error {
	buck, err := o.Client.Bucket(bucket)
	if err != nil {
		return ossToObjectError(err, bucket, object)
	}

	// 使用OSS原生接口中止分片上传
	err = buck.AbortMultipartUpload(oss.InitiateMultipartUploadResult{
		Bucket:   bucket,
		Key:      object,
		UploadID: uploadID,
	})
	if err != nil {
		return ossToObjectError(err, bucket, object)
	}

	return nil
}

// CompleteMultipartUpload - completes a multipart upload.
func (o *ossObjects) CompleteMultipartUpload(ctx context.Context, bucket, object, uploadID string, uploadedParts []minio.CompletePart, opts minio.ObjectOptions, storageAccountName string) (objInfo minio.ObjectInfo, err error) {
	buck, err := o.Client.Bucket(bucket)
	if err != nil {
		return objInfo, ossToObjectError(err, bucket, object)
	}

	// 转换分片信息为OSS格式
	var completeParts []oss.UploadPart
	for _, part := range uploadedParts {
		completeParts = append(completeParts, oss.UploadPart{
			PartNumber: part.PartNumber,
			ETag:       part.ETag,
		})
	}

	// 使用OSS原生接口完成分片上传
	_, err = buck.CompleteMultipartUpload(oss.InitiateMultipartUploadResult{
		Bucket:   bucket,
		Key:      object,
		UploadID: uploadID,
	}, completeParts)
	if err != nil {
		return objInfo, ossToObjectError(err, bucket, object)
	}

	// 获取对象信息
	header, err := buck.GetObjectMeta(object)
	if err != nil {
		return objInfo, ossToObjectError(err, bucket, object)
	}

	size, _ := strconv.ParseInt(header.Get("Content-Length"), 10, 64)
	modTime, _ := http.ParseTime(header.Get("Last-Modified"))

	return minio.ObjectInfo{
		Bucket:      bucket,
		Name:        object,
		ModTime:     modTime,
		Size:        size,
		ETag:        strings.Trim(header.Get("ETag"), "\""),
		ContentType: header.Get("Content-Type"),
	}, nil
}

// 根据文件名获取内容类型
func getContentType(name string) string {
	ext := filepath.Ext(name)
	switch ext {
	case ".html", ".htm":
		return "text/html"
	case ".css":
		return "text/css"
	case ".js":
		return "application/javascript"
	case ".jpg", ".jpeg":
		return "image/jpeg"
	case ".png":
		return "image/png"
	case ".gif":
		return "image/gif"
	case ".pdf":
		return "application/pdf"
	case ".txt":
		return "text/plain"
	case ".mp4":
		return "video/mp4"
	case ".mp3":
		return "audio/mpeg"
	case ".json":
		return "application/json"
	case ".xml":
		return "application/xml"
	case ".zip":
		return "application/zip"
	case ".tar":
		return "application/x-tar"
	case ".gz", ".gzip":
		return "application/gzip"
	default:
		return "application/octet-stream"
	}
}

// GetBucketPolicy - Get the bucket ACL and convert it to canonical []bucketAccessPolicy
func (l *ossObjects) GetBucketPolicy(ctx context.Context, bucket string, storageAccountName string) (*policy.Policy, error) {
	// 获取 bucket 的 ACL
	aclRes, err := l.Client.GetBucketACL(bucket)
	if err != nil {
		logger.LogIf(ctx, err)
		return nil, ossToObjectError(err, bucket)
	}

	var policyInfo = &policy.Policy{
		Version: policy.DefaultVersion,
	}

	// 将 OSS ACL 转换为 MinIO policy
	switch aclRes.ACL {
	case "private":
		// 私有访问，不需要添加任何策略
		return policyInfo, nil
	case "public-read":
		// 公共读取访问
		readOnlyStmt := policy.Statement{
			Actions: policy.NewActionSet(
				policy.GetObjectAction,
				policy.ListBucketAction,
			),
			Effect:    policy.Allow,
			Principal: policy.NewPrincipal("*"),
			Resources: policy.NewResourceSet(
				policy.NewResource(bucket, ""),
				policy.NewResource(bucket, "*"),
			),
		}
		policyInfo.Statements = []policy.Statement{readOnlyStmt}
		return policyInfo, nil
	case "public-read-write":
		// 公共读写访问
		readWriteStmt := policy.Statement{
			Actions: policy.NewActionSet(
				policy.GetObjectAction,
				policy.ListBucketAction,
				policy.PutObjectAction,
			),
			Effect:    policy.Allow,
			Principal: policy.NewPrincipal("*"),
			Resources: policy.NewResourceSet(
				policy.NewResource(bucket, ""),
				policy.NewResource(bucket, "*"),
			),
		}
		policyInfo.Statements = []policy.Statement{readWriteStmt}
		return policyInfo, nil
	default:
		logger.LogIf(ctx, fmt.Errorf("Unsupported bucket ACL: %s", aclRes.ACL))
		return nil, minio.NotImplemented{}
	}
}
