apiVersion: karpenter.k8s.oracle/v1alpha1
kind: OciNodeClass
metadata:
  name: karpenter-test
spec:
  bootConfig:
    bootVolumeSizeInGBs: 100
    bootVolumeVpusPerGB: 10
  agentList:
    - Bastion
  imageSelector:
    - name: okeworker
      compartmentId: ocid1.compartment.oc1..aaaaaaaau5q457a7teqkjce4oenoiz6bmc4g3s74a5543iqbm7xwplho44fq
  imageFamily: Custom
  kubelet:
    evictionHard:
      imagefs.available: 15%
      imagefs.inodesFree: 10%
      memory.available: 750Mi
      nodefs.available: 10%
      nodefs.inodesFree: 5%
    systemReserved:
      memory: 100Mi
  subnetSelector:
    - name: worker
  vcnId:  ocid1.vcn.oc1.ap-melbourne-1.amaaaaaaak7gbrian6cl4ka3dbnxqzp6slf2fgygv6maigurx4lnjkdszzbq
  # userData: |
  #   #!/bin/bash -xe
  #   bash /etc/oke/oke-install.sh --apiserver-endpoint '10.0.0.14' --kubelet-ca-cert 'LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURpVENDQW5HZ0F3SUJBZ0lSQUtKeVJBVHRDSEl2YUd4Ry9OQ2VqTTR3RFFZSktvWklodmNOQVFFTEJRQXcKWGpFUE1BMEdBMVVFQXd3R1N6aHpJRU5CTVFzd0NRWURWUVFHRXdKVlV6RVBNQTBHQTFVRUJ3d0dRWFZ6ZEdsdQpNUTh3RFFZRFZRUUtEQVpQY21GamJHVXhEREFLQmdOVkJBc01BMDlqYVRFT01Bd0dBMVVFQ0F3RlZHVjRZWE13CkhoY05NalV3TmpFM01ESXhOVEU1V2hjTk16QXdOakUzTURJeE5URTVXakJlTVE4d0RRWURWUVFEREFaTE9ITWcKUTBFeEN6QUpCZ05WQkFZVEFsVlRNUTh3RFFZRFZRUUhEQVpCZFhOMGFXNHhEekFOQmdOVkJBb01Cazl5WVdOcwpaVEVNTUFvR0ExVUVDd3dEVDJOcE1RNHdEQVlEVlFRSURBVlVaWGhoY3pDQ0FTSXdEUVlKS29aSWh2Y05BUUVCCkJRQURnZ0VQQURDQ0FRb0NnZ0VCQUttT3JaRmpKMFRJRU9vL0JxaHBPZ2xiR21pTFA1ckhsY04xZWI0dXZCSHUKNGN5NWN2MjJzeFRoNWFvVWdYQU1sVkt6bjFvbk5sRHVkdkhzaXNqZCthY1V0YVdvOFR0WFFtMm0xUkNuS3RPagpnUWY1UVlZbC9NYXpYRkFYTWxuaUpUT0tXNXVOVnRoRlZXcWRyRnpCMGhMcldyQXkvN21xSUhnUVhOTVdodTZsCmNSTGgxMHphUWtxUHp5b0V5TFpqMlRmOWRhcnlzaXkrcTZCcUcxbUhYWEU4RmZhenAzT0x1RVV3WFFmZWt1dmQKZHlSNG1mSzhsVXJNZWFuMERQSHJWT2JxWE9XZGhTeGNkMktITE9pbVh5bzl5TE1xZ1NmL0QyQjFhMDlDbCs5Lwo4dWhWdkRuekM2NHEwczdkb0taTTFFb1Y4dmN6VFZLZ2pkcXQ0eWhYMmNzQ0F3RUFBYU5DTUVBd0R3WURWUjBUCkFRSC9CQVV3QXdFQi96QU9CZ05WSFE4QkFmOEVCQU1DQVFZd0hRWURWUjBPQkJZRUZORTBudDJ1NFRlZjZqMnIKd0duaEEwN0t3MlVGTUEwR0NTcUdTSWIzRFFFQkN3VUFBNElCQVFBa1NSNklhWjFQbVNVQkZHN2pDS1VTdlBCTgpLcDRsUk9mb0ZTK3l4aEdMQVBkNEc2TEppT3ZlaGVZNS94b2dMYll5ZUR4S3RVNmQrSFFZNVp0U1U1MHhLVFExCitMNVpWUU5sVEVvV0c0ZUNvcUZVeXVJdGZoV1U0em5wQTBtR1RXa0tncUNIZk0vdlBRZDEwQ25YbExSYWVTZW4KSjQ2WEUwZm1GN1ZOMUVsUy9UdkJZd1RMR1V1N2dRdEtiZmV5MTRQbEZTSlRmOS9xK0ZHaVFnZlZXUFNBRE9aVQpCbkxrcUV0NGVVQnFIR0JFY0FQSnpXZE1haU8vNEhTNGE0Y1p1Q05mSkhpbXhoR0Q2RWZHYUVqZVZXVG8zcjl1ClFrajQ5UE1oTnNOV1JQbDRCSHlHdFlNRjRTVHlGTVFhVGFOQlBpL2pLbnJzQmo3U1Q1SzI5aUs2c0R6agotLS0tLUVORCBDRVJUSUZJQ0FURS0tLS0tCg==' \
  #   –kubelet-extra-args '–node-labels="karpenter.sh/nodepool=karpenter-test,servicegroup=karpenter-test" --max-pods=110 --system-reserved="memory=100Mi" --eviction-hard="imagefs.inodesFree<10%,memory.available<750Mi,nodefs.available<10%,nodefs.inodesFree<5%,imagefs.available<15%"' \
  #   –cluster-dns '10.96.5.5'